{"name": "mobile_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.710.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/joy": "^5.0.0-beta.51", "@mui/material": "^6.2.0", "@mui/x-date-pickers": "^7.24.0", "axios": "^1.7.9", "base-64": "^1.0.0", "browser-image-compression": "^2.0.2", "cross-blob": "^3.0.2", "dayjs": "^1.11.13", "react": "^18.3.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-router-dom": "^7.0.2", "react-webcam": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/base-64": "^1.0.2", "@types/node": "^22.15.21", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}