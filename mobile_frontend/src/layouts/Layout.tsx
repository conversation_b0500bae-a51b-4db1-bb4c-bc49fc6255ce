import { Box } from "@mui/material";
import Navbar from "../components/Navbar";

type LayoutProps = {
  children: React.ReactNode;
  title: string;
};

const Layout: React.FC<LayoutProps> = ({ children, title }) => {
  return (
    <>
      <Box sx={{ height: "100%", overflow: "hidden" }}>
        <Navbar title={title} />
        <Box sx={{ display: "flex", height: "100%" }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              flexGrow: 1,
              p: 2,
              pb: 0.25,
              mt: 8,
              ml: "1%",
              mr: "1%",
            }}>
            {children}
          </Box>
        </Box>
      </Box>
    </>
  );
};
export default Layout;
