import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import LoginPage from "./pages/LoginPage";
// import ProtectedRoute from "./utils/ProtectedRoute";
import TopPage from "./pages/TopPage";

const Router = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<LoginPage />} />

        <Route>
          {/* <Route path="/" element={<HomePage />} /> */}
          <Route path="/" element={<TopPage />} />
        </Route>

        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </BrowserRouter>
  );
};

export default Router;
