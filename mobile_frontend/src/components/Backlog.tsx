import { Box, styled, SwipeableDrawer, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { grey } from "@mui/material/colors";
import { useState } from "react";

import { useTasks } from "../utils/TaskContext";
import { useDialog } from "../utils/DialogContext";
import { CardTask, BaseTask } from "../types";
import TaskCard from "./TaskCard";
import { Global } from "@emotion/react";

type BackLogProps = {
  tasks: CardTask[];
};
// 展開前のバックログエリアの高さ
const drawerBleeding = 100;
const StyledBox = styled("div")(({ theme }) => ({
  backgroundColor: "#fff",
  ...theme.applyStyles("dark", {
    backgroundColor: grey[800],
  }),
}));

const Puller = styled("div")(({ theme }) => ({
  width: 30,
  height: 6,
  backgroundColor: grey[300],
  borderRadius: 3,
  position: "absolute",
  top: 8,
  left: "calc(50% - 15px)",
  ...theme.applyStyles("dark", {
    backgroundColor: grey[900],
  }),
}));

export default function Backlog({ tasks }: BackLogProps) {
  const { createNewTask } = useTasks();
  const { openDialog } = useDialog();
  const [isBacklogOpen, setIsBacklogOpen] = useState<boolean>(false);

  const toggleDrawer = (newOpen: boolean) => () => {
    setIsBacklogOpen(newOpen);
  };
  const handleAddCard = () => {
    const newTask = createNewTask();
    openDialog("new", newTask);
  };

  const handleTaskClick = (dialogData: BaseTask) => {
    try {
      openDialog("backlog", dialogData);
    } catch (error) {
      console.error("タスクをクリックする際にエラーが出た:", error);
    }
  };

  return (
    <>
      {/* バックログの表示領域をタイムラインと別に確保するための空の箱 */}
      <Box sx={{ height: drawerBleeding, flexShrink: 0 }}></Box>
      <Global
        styles={{
          ".MuiDrawer-root > .MuiPaper-root": {
            height: `calc(80% - ${drawerBleeding}px)`,
            overflow: "visible",
          },
        }}
      />
      <SwipeableDrawer
        anchor="bottom"
        open={isBacklogOpen}
        onClose={toggleDrawer(false)}
        onOpen={toggleDrawer(true)}
        swipeAreaWidth={drawerBleeding}
        disableSwipeToOpen={false}
        keepMounted>
        <StyledBox
          sx={{
            position: "absolute",
            top: -drawerBleeding,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8,
            visibility: "visible",
            right: 0,
            left: 0,
            boxShadow: 10,
          }}>
          <Box
            sx={{
              marginTop: 3,
              border: "1px solid gray",
              padding: 0,
              backgroundColor: "#EBEBEB",
            }}>
            <Puller />
            <Typography sx={{ textAlign: "center" }}>未割当作業・繰越作業</Typography>
          </Box>
          <Box>
            <Box
              sx={{
                overflowY: "auto",
                mt: 1,
                height: `calc(80vh - 110px)`,
              }}>
              {tasks.map((item) => {
                return (
                  <Box
                    sx={{
                      position: "relative",
                      width: "100%",
                      display: "flex",
                      justifyContent: "center",
                      mb: 0.5,
                    }}
                    key={item.cardId}>
                    <TaskCard
                      item={item}
                      sxWidth={"95%"}
                      sxHeight={35}
                      onTaskClick={handleTaskClick}
                      isBacklog={true}
                    />
                  </Box>
                );
              })}
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                marginRight: 1,
              }}>
              <Box
                component={"span"}
                onClick={handleAddCard}
                sx={{
                  color: "gray",
                  border: "1px solid gray",
                  borderRadius: "50%",
                  width: "28px",
                  height: "28px",
                  boxShadow: "0px 5px 10px 0px rgba(0, 0, 0, 0.35)",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  ":active": { width: "25px", height: "25px" },
                }}>
                <AddIcon />
              </Box>
            </Box>
          </Box>
        </StyledBox>
      </SwipeableDrawer>
    </>
  );
}
