import { Box, TextField } from "@mui/material";
import { SetStateAction, useState } from "react";

const getAnnouncement = () => {
  const text = `・GW期間の繁忙対応お願いします
・本日雨、気温も上がらず 明日以降は晴れ予報
・YouTuberコラボ商品の取扱い開始
・ピーコラボフードウェア展開開始
`;
  return text;
};

export default function AnnouncementArea() {
  const [announcement, setAnnouncement] = useState(getAnnouncement());

  const handleTextFieldChange = (event: {
    target: { value: SetStateAction<string> };
  }) => {
    setAnnouncement(event.target.value);
  };

  return (
    <Box>
      <TextField
        id="announcement_textfield"
        label="全体連絡事項"
        multiline
        rows={2.5}
        value={announcement}
        fullWidth
        onChange={handleTextFieldChange}
        sx={{ mt: 2, mb: 1 }}
        slotProps={{
          input: {
            readOnly: true,
          },
        }}
        size="small"
      />
    </Box>
  );
}
