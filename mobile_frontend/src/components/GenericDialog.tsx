import React from "react";
import "../App.css";
import {
  Box,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import "dayjs/locale/ja";
import { useGenericDialog } from "../utils/GenericDialogContext";


const Dialog = () => {
  const { isOpen, content, closeDialog } = useGenericDialog();

  if (!isOpen) {
    return null;
  } else {
    return (
      <>
        <Box
          sx={{
            display: "flex",
            justifyContent: "right",
            alignItems: "center",
            mb: 2,
            position: "sticky",
            backgroundColor: "white",
            zIndex: 1,
          }}
        >
          <IconButton onClick={closeDialog}>
            <CloseIcon />
          </IconButton>
        </Box>
        {content && React.createElement(content)}
      </>
    );
  }
};

export default Dialog;
