import { useState } from "react";
import "../App.css";
import {
  Box,
  Button,
  Typography,
  Stack,
  DialogActions,
} from "@mui/material";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
// import GroupIcon from "@mui/icons-material/Group";
// import GraphicEqIcon from "@mui/icons-material/GraphicEq";
import "dayjs/locale/ja";
import { useGenericDialog } from "../utils/GenericDialogContext";
import { isBaseTask } from "../types";
import { useTasks } from "../utils/TaskContext";
import { convertToDayjs } from "../utils/DayjsConverter";
import { dataAlignerOnSwipe } from "../utils/taskDataAlignment";

const Dialog = () => {
  const { props: task, closeDialog } = useGenericDialog();

  // BaseTask型が来ることを前提にする。それ以外の型であれば空でreturn
  if (!isBaseTask(task)) { return <></> }


  const { updateTask } = useTasks();
  const [endTime, setEndTime] = useState<Date>(new Date());

  const submitCompletion = () => {
    updateTask(dataAlignerOnSwipe(task, endTime))
    closeDialog()
  }


  return (
    <>

      <Stack
        spacing={3}
        sx={{
          margin: 2,
        }}
      >
        <Box>
          <Typography sx={{ mb: 1 }}>「{task.taskName}」を完了にします。
          </Typography>
          <LocalizationProvider
            dateAdapter={AdapterDayjs}
            adapterLocale="ja"
            key={task?.taskId}
          >

            <DateTimePicker
              openTo="hours"
              label="完了時刻"
              value={convertToDayjs(endTime)}
              onChange={(newValue) => setEndTime(newValue?.toDate() ?? new Date())}
              minDateTime={convertToDayjs(task?.start ?? new Date()) ?? undefined}
              maxDateTime={convertToDayjs(new Date(new Date().getTime() + (60 * 1000))) ?? undefined}
              sx={{ width: "200px" }}
            />
          </LocalizationProvider>
          <DialogActions>
            <Button onClick={closeDialog}>キャンセル</Button>
            <Button onClick={submitCompletion}>決定</Button>
          </DialogActions>
        </Box>
      </Stack>
    </>
  );
}
export default Dialog;
