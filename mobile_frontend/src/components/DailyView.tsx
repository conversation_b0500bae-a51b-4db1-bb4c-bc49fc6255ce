import { useEffect, useRef } from "react";
import { Box, Typography, TextField, Dialog as MuiDialog } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

import Dialog from "./Dialog";
import { useDialog } from "../utils/DialogContext";
import { useTasks } from "../utils/TaskContext";
import AnnouncementArea from "./AnnouncementArea";
import TaskCard from "./TaskCard";
import { BaseTask } from "../types/task";
import Backlog from "./Backlog";
import NotificationArea from "./NotificationArea";
import { useGenericDialog } from "../utils/GenericDialogContext";
import GenericDialog from "./GenericDialog";

const rowHeight = 70;
const rowWidth = 60;

export default function DailyView() {
  const {
    refreshTasks,
    calendarTasks,
    backlogTasks,
    selectedDate,
    setSelectedDate,
    assignedName,
    setAssignedName,
  } = useTasks();

  const hours = Array.from(
    { length: 24 },
    (_, i) => `${i.toString().padStart(2, "0")}:00`
  );
  const scrollRef = useRef<HTMLDivElement>(null);
  // FIXME: 5/2検証向け対応として4/22 9:05に固定
  const currentDatetime = new Date("2025-05-02T09:05:00.000000"); // new Date()
  const currentHour = dayjs(currentDatetime).hour();
  const currentMinute = dayjs(currentDatetime).minute();
  const currentTimePos =
    currentHour * rowHeight + (currentMinute / 60) * rowHeight;
  const isToday =
    dayjs(currentDatetime).format("YYYY-MM-DD") ==
    selectedDate.format("YYYY-MM-DD");
  // Lv.2検証向けデフォルト設定として田中を設定
  const didScrollRef = useRef(false);

  useEffect(() => {
    // 初期表示や担当者・日付変更時にタスクをリフレッシュ
    refreshTasks(dayjs(selectedDate).format("YYYY-MM-DD"), assignedName);
  }, [selectedDate, assignedName]);
  useEffect(() => {
    if (!didScrollRef.current && scrollRef.current && isToday) {
      scrollRef.current.scrollTop = currentTimePos - 100;
      didScrollRef.current = true;
    }
  });

  const { isOpen, openDialog, closeDialog } = useDialog();
  const genericDialog = useGenericDialog();
  const handleTaskClick = (dialogData: BaseTask) => {
    try {
      openDialog("view", dialogData);
    } catch (error) {
      console.error("タスクをクリックする際にエラーが出た:", error);
    }
  };

  // const currentUser = "Aさん"; // 暫定処置、Aさんのデータだけを表示する
  // const currentUser = "田中"; // Lv.2検証向けデフォルト設定。データ準備次第コメントアウト解除して上の行をコメントアウト

  const calcCardPos = (item: BaseTask) => {
    // const sxTop =
    //   item.start.getHours() * rowHeight +
    //   rowHeight * (item.start.getMinutes() / 60);

    // return { sxTop: sxTop };
    const start = dayjs(item.start);
    const minutesFromMidnight = start.diff(start.startOf("day"), "minute");
    return {
      sxTop: (minutesFromMidnight / 60) * rowHeight,
    };
  };

  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Box>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              sx={{
                mr: 1,
                // 高さをTextFieldのsize: smallに合わせる
                "> .MuiOutlinedInput-root": {
                  height: 40,
                },
              }}
              label="日付"
              value={selectedDate}
              defaultValue={selectedDate}
              onChange={(newValue) =>
                setSelectedDate(newValue ?? dayjs(new Date()))
              }
              format={"YYYY/MM/DD"}
            />
          </LocalizationProvider>
        </Box>
        <Box>
          <TextField
            label={"担当"}
            value={assignedName}
            onChange={(e) => setAssignedName(e.target.value)} // TODO: 今後は担当者変更不可にする
            size="small"
          />
        </Box>
      </Box>
      <NotificationArea />
      <AnnouncementArea />
      <Box
        ref={scrollRef} // 現在時刻までscroll
        sx={{
          width: "100%",
          maxWidth: 600,
          height: 600,
          overflowY: "auto",
          border: "2px solid gray",
          position: "relative",
          overflowX: "hidden",
        }}
      >
        {/* 現在時刻用 */}
        <Box
          sx={{
            position: "absolute",
            top: currentTimePos,
            left: rowWidth,
            height: "1px",
            width: `calc(100% - ${rowWidth}px)`,
            backgroundColor: "#FF82B2",
            opacity: isToday ? 1 : 0,
            zIndex: 10,
          }}
        />
        {/* 現在時刻用 */}
        <Box
          sx={{
            position: "absolute",
            top: currentTimePos - 5,
            left: rowWidth - 5,
            height: 10,
            width: 10,
            borderRadius: "50%",
            backgroundColor: "#FF82B2",
            opacity: isToday ? 1 : 0,
            zIndex: 10,
          }}
        />
        {hours.map((hour) => (
          <Box
            key={hour}
            sx={{
              boxSizing: "border-box",
              borderTop: "0.5px solid gray",
              height: rowHeight,
              display: "flex",
            }}
          >
            {/* 左側時間表示 */}
            <Box
              sx={{
                borderRight: "0.5px solid gray",
                width: rowWidth,
                minWidth: rowWidth,
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxSizing: "border-box",
              }}
            >
              <Typography>{hour}</Typography>
            </Box>
            {/* 30分間隔のborder表示 */}
            <Box
              sx={{
                borderBottom: "0.5px dotted gray",
                height: rowHeight / 2,
                width: "100%",
              }}
            />
          </Box>
        ))}
        {/* タスクカードをここに絶対配置 */}
        {calendarTasks.map((task) => (
          <Box
            key={task.cardId}
            sx={{
              position: "absolute",
              top: calcCardPos(task).sxTop,
              left: rowWidth,
              width: `calc(100% - ${rowWidth}px)`,
              zIndex: 5,
            }}
          >
            <TaskCard
              item={task}
              sxWidth="100%"
              sxHeight={null}
              onTaskClick={handleTaskClick}
              isBacklog={false}
            />
          </Box>
        ))}
      </Box>
      <Backlog tasks={backlogTasks} />
      <MuiDialog
        open={isOpen}
        onClose={closeDialog}
        sx={{ "& .MuiPaper-root": { width: "100%" } }}
      >
        <Dialog />
      </MuiDialog>
      <MuiDialog
        open={genericDialog.isOpen}
        onClose={genericDialog.closeDialog}
        sx={{ "& .MuiPaper-root": { width: "100%" } }}
      >
        <GenericDialog />
      </MuiDialog>
    </>
  );
}
