import Box from "@mui/material/Box";
import GroupIcon from "@mui/icons-material/Group";
import GraphicEqIcon from "@mui/icons-material/GraphicEq";
import { TouchEvent, useRef, useEffect } from "react";
import { CardTask, BaseTask } from "../types";
import { useTasks } from "../utils/TaskContext";
import { dataAlignerOnDoubleTap } from "../utils/taskDataAlignment";
import { useGenericDialog } from "../utils/GenericDialogContext";
import CompletionConfirm from "./CompletionConfirm";


type TaskProps = {
  item: CardTask;
  sxWidth: number | string;
  sxHeight: number | null;
  onTaskClick: (dialogData: BaseTask) => void;
  isBacklog: boolean;
};

// DailyViewと同じ値を指定する。px
const rowHeight = 70
export default function TaskCard({
  item,
  sxWidth,
  sxHeight,
  onTaskClick,
  isBacklog,
}: TaskProps) {
  const { updateTask } = useTasks();
  const genericDialog = useGenericDialog()
  const cardRef = useRef<HTMLElement>(null);
  const calculateHeight = () => {
    if (item.start && item.end) {
      const durationMinutes = (item.end.getTime() - item.start.getTime()) / 60000;
      return durationMinutes * (rowHeight / 60);
    } else {
      return 0
    }
  };
  const calculatePy = () => {
    const defaultPadding = 0.5 // 0.5 = 4px
    const height = calculateHeight()
    // height > defaultPadding * 8px * top&bottom + fontsize
    if (height > ((defaultPadding * 8) * 2 + 14)) {
      return defaultPadding
    } else {
      return 0
    }
  }

  // touch-action: "manipulation"でダブルタップズームを抑止しているが、これを入れるとブラウザ側でtouchstartからtouchmove以降へのイベント伝播がキャンセルされる
  // これを防ぐためtouchstartのブラウザ動作をpreventDefault()でキャンセルしたいが、
  // Reactではtouchイベントはデフォルトでpassive: trueになっているため、ReactのOnTouchXXXハンドラー内でpreventDefaultすることができないので
  // 個別にDOM要素を取得しデフォルトイベントをキャンセルしている
  const setPreventDefault = (event: Event) => {
    event.preventDefault();
  };
  useEffect(() => {
    cardRef.current?.addEventListener("touchstart", setPreventDefault, {
      passive: false,
    });

    return () => {
      cardRef.current?.removeEventListener("touchstart", setPreventDefault);
    };
  });

  const returnSourceIcon = (source: string) => {
    if (source === "teams") return <GroupIcon />;
    if (source === "インカム") return <GraphicEqIcon />;
    return null;
  };

  const sxBackGroundColor = (status: string) => {
    if (status === "未着手") return "#FFFFFF";
    if (status === "進行中") return "#C2DCF6";
    if (status === "完了") return "#D5CDCD";
    if (status === "中断") return "#E8A7AC";
    return "#ffffff";
  };

  // 締切超過判定
  const isOverDueDate = (due: Date | null | undefined) => {
    const today = new Date();
    if (due == null || due == undefined) return false;
    if (today > due) return true;
    return false;
  };
  // スワイプで完了にする処理
  const defaultCardLeft = 0;
  let prevX: number;
  let prevY: number;

  const completeAction = (item: CardTask) => {
    genericDialog.openDialog(CompletionConfirm, item.baseTask ?? {})

  }
  // 完了処理が行われたときだけ、trueを返す
  const completeTaskOnSwipe = (
    event: TouchEvent<HTMLDivElement>,
    type: string
  ): boolean => {
    switch (type) {
      case "start":
        prevX = event.targetTouches[0].clientX;
        prevY = event.targetTouches[0].clientY;
        return false;
      case "move":
        if (
          Math.abs(prevX - event.targetTouches[0].clientX) -
          Math.abs(prevY - event.targetTouches[0].clientY) > 1
        ) {
          const leftPx = Number(
            event.currentTarget.style.marginLeft.slice(0, -2)
          );
          if (Number.isNaN(leftPx)) {
            event.currentTarget.style.marginLeft = defaultCardLeft + "px";
          } else {
            const movingDistance =
              leftPx + (event.targetTouches[0].clientX - prevX) * 1.3;
            event.currentTarget.style.marginLeft =
              (movingDistance > 5 ? movingDistance : defaultCardLeft) + "px";
          }
        } else {
          // カードの上でタップしても縦方向にスクロールできる処理
          // カードの二つ上のdiv要素をスクロール要素と扱っているため、本当はidなどで明示的にスクロール要素を取得させた方が壊れにくい
          // TODO: 慣性スクロールを適用したい
          const scrollElement = event.currentTarget.parentElement?.parentElement
          if (Math.abs(prevY - event.targetTouches[0].clientY) > 2 && scrollElement) {
            scrollElement.scrollTop = scrollElement.scrollTop + (prevY - event.targetTouches[0].clientY)
          }
        }
        prevX = event.targetTouches[0].clientX;
        prevY = event.targetTouches[0].clientY;

        return false;
      case "end": {
        let isStatusUpdated = false;
        const movedLeft = Number(
          event.currentTarget.style.marginLeft.slice(0, -2)
        );

        if (!Number.isNaN(movedLeft) && movedLeft - defaultCardLeft > 50) {
          if (item) {
            completeAction(item)
          }
          isStatusUpdated = true;
        }
        event.currentTarget.style.marginLeft = defaultCardLeft + "px";

        return isStatusUpdated;
      }
      default:
        return false;
    }
  };
  const lastTapRef = useRef(0); // 直近touchendが起きた時間
  const lastDbTap = useRef(false); // 直近 {DOUBLE_TAP_THRESHOLD}ms のタッチはダブルタップを意図したものか
  const lastScrollTop = useRef(0); // タッチが始まったときのスクロール位置
  const DOUBLE_TAP_THRESHOLD = 200; // ダブルタップとみなす範囲（ミリ秒）
  const suspendTaskOnDbTap = (type: string): boolean => {
    switch (type) {
      case "end":
        {
          let executionFlag = false;
          const now = Date.now();
          const timeSinceLastTap = now - lastTapRef.current;

          // ダブルタップの場合（最後のTouchEndイベントからの時間がDOUBLE_TAP_THRESHOLD以下）
          if (timeSinceLastTap < DOUBLE_TAP_THRESHOLD && timeSinceLastTap > 0 && item.status !== "未着手") {
            if (item) {
              const formattedTask = dataAlignerOnDoubleTap(item)
              updateTask(formattedTask);
            }
            executionFlag = true;
            lastDbTap.current = true
          }

          lastTapRef.current = now;
          return executionFlag
        }
      default:
        return false


    }
  }
  const handleTouchStart = (event: TouchEvent<HTMLDivElement>) => {
    if (!isBacklog && item.status !== "未着手") {
      completeTaskOnSwipe(event, "start");
      lastScrollTop.current = event.currentTarget.parentElement?.parentElement?.scrollTop ?? 0
    }
  };

  const handleTouchMove = (event: TouchEvent<HTMLDivElement>) => {
    if (!isBacklog && item.status !== "未着手") {
      completeTaskOnSwipe(event, "move");
    }
  };
  const handleTouchEnd = (event: TouchEvent<HTMLDivElement>) => {
    if (!isBacklog && item.status !== "未着手") {
      const swipeResult = completeTaskOnSwipe(event, "end");
      let dbTapResult = false
      if (!swipeResult) dbTapResult = suspendTaskOnDbTap("end")
      // ダブルタップ扱いにする時間を経過してからダイアログを開く
      const scrollTop = event.currentTarget.parentElement?.parentElement?.scrollTop ?? 0
      setTimeout(() => {
        if (
          !swipeResult // スワイプ処理が走っていない
          && !dbTapResult // ダブルタップ処理が走っていない
          && !lastDbTap.current // ダブルタップのうちの1回目のタップではない
          && Math.abs(lastScrollTop.current - scrollTop) < 2 // スクロールではない
        ) handleClick()
        lastDbTap.current = false
      }, DOUBLE_TAP_THRESHOLD)

    } else {
      handleClick()
    }
  };

  const handleClick = () => {
    try {
      onTaskClick({
        ...item.baseTask,
      });
    } catch (error) {
      console.error("タスクカードをクリックした際にエラーが発生:", error);
    }
  };

  return (
    <Box
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      sx={{
        height: isBacklog ? sxHeight : calculateHeight(),
        width: sxWidth,
        boxSizing: "border-box",
        border: isOverDueDate(item.dueDate)
          ? "2px red solid"
          : "2px gray solid",
        borderRadius: "4px",
        backgroundColor: sxBackGroundColor(item.status),
        px: 1,
        py: calculatePy(),
        marginBottom: 0,
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        overflow: "hidden",
        minHeight: isBacklog ? 0 : 10,
        userSelect: "none", //将来的にカードを押す際に、文字を選択させたくない場合、この行のコメントを解除する。
        touchAction: "manipulation", // ダブルタップ時の拡大抑止
      }}
      ref={cardRef}>
      {isBacklog ? (
        <>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Box sx={{ fontSize: "default", fontWeight: "bold", maxWidth: "70%" }}>
              {item.taskName}
            </Box>
            <Box sx={{ fontSize: 10 }}>{item.assignedName}</Box>
          </Box>
        </>
      ) : (
        <>
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
            <Box sx={{ fontSize: "default", fontWeight: "bold" }}>{item.taskName}</Box>
            <Box sx={{ fontSize: 10 }}>{item.location}</Box>
          </Box>
          <Box sx={{ display: "flex", justifyContent: "flex-end" }}>
            {returnSourceIcon(item.source ?? "")}
          </Box>
        </>
      )}
    </Box>
  );
}