import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import AccountCircleOutlinedIcon from "@mui/icons-material/AccountCircleOutlined";
import MenuItem from "@mui/material/MenuItem";
import Menu from "@mui/material/Menu";
import { useLocation, useNavigate } from "react-router-dom";
import { useContext } from "react";
import { UserContext } from "../utils/AccountContext";
import { BASE_URL } from "../utils/api";

interface NavbarProps {
  title: string;
}

export default function Navbar({ title }: NavbarProps) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const location = useLocation();
  const isTaskDetailPage = /^\/task\/[^/]+(\/[^/]+)?$/.test(location.pathname);
  const navigate = useNavigate();
  const { setUsername } = useContext(UserContext);
  const handleLogout = async () => {
    try {
      const response = await fetch(`${BASE_URL}/auth/logout`, {
        method: "POST",
        credentials: "include",
      });

      if (!response.ok) {
        console.warn("Server logout failed, cleaning up locally");
      }

      localStorage.removeItem("username");
      sessionStorage.clear();
      setUsername("");

      navigate("/login", { replace: true });
    } catch (error) {
      console.error("Logout error:", error);
      localStorage.removeItem("username");
      sessionStorage.clear();
      setUsername("");
      navigate("/login", { replace: true });
    }
  };
  const handleReturn = () => {
    navigate("/tasks");
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppbarのzIndexをdrawerの上に置く（drawerのzIndexは1200） */}
      <AppBar
        position="fixed"
        sx={{ zIndex: 1201, backgroundColor: "#519BE5" }}
      >
        <Toolbar>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              position: "relative",
              width: "100%",
            }}
          >
            <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
              {isTaskDetailPage && (
                <IconButton
                  edge="start"
                  color="inherit"
                  aria-label="return"
                  onClick={handleReturn}
                  sx={{ mr: 2 }}
                >
                  <ArrowBackIcon />
                </IconButton>
              )}
            </Box>
            <Box
              sx={{
                position: "absolute",
                left: "50%",
                transform: "translateX(-50%)",
                textAlign: "center",
              }}
            >
              <Typography align="center" fontSize={"22px"}>
                {title}
              </Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <IconButton
                size="large"
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                onClick={handleMenu}
                color="inherit"
              >
                <Box>
                  <AccountCircleOutlinedIcon />
                  {/* <Typography variant="body2">
                    {AllUsername[username]}
                  </Typography> */}
                </Box>
              </IconButton>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                keepMounted
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
              >
                <MenuItem onClick={() => navigate("/name")}>
                  <Typography variant="body2">名前設定</Typography>
                </MenuItem>
                <MenuItem onClick={handleLogout}>
                  <Typography variant="body2">ログアウト</Typography>
                </MenuItem>
              </Menu>
            </Box>
          </Box>
        </Toolbar>
      </AppBar>
    </Box>
  );
}
