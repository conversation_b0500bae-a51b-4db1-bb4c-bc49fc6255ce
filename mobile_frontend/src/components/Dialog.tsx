import React, { useState, useEffect, useMemo, SetStateAction } from "react";
import "../App.css";
import {
  Box,
  TextField,
  Button,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  IconButton,
  Stack,
  FormControl,
  Select,
  MenuItem,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from '@mui/icons-material/Add';
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Link } from "@mui/material";
// import GroupIcon from "@mui/icons-material/Group";
// import GraphicEqIcon from "@mui/icons-material/GraphicEq";
import "dayjs/locale/ja";
import dayjs, { Dayjs } from "dayjs";
import { useDialog } from "../utils/DialogContext";
import { DialogTask, isBaseTask } from "../types";
import { useTasks } from "../utils/TaskContext";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { DateTimeValidationError } from "@mui/x-date-pickers/models";
import { dataAlignerOnEdit, dataAlignerOnHistoryEdit } from "../utils/taskDataAlignment";
import { convertToDayjs } from "../utils/DayjsConverter";

const Dialog: React.FC = () => {
  const { isOpen, mode, task, setMode, closeDialog } = useDialog();
  const { tasks, updateTask, createNewTask, assignedName, setSelectedDate } =
    useTasks();
  const [dialogTask, setDialogTask] = useState<DialogTask>({
    taskId: "",
    taskName: "",
    status: "未着手",
    category: "",
    location: "",
    scheduledTime: 60,
    details: "",
    source: "",
    assignedName: "",
    start: new Date(),
    end: new Date(),
    mode: mode,
    dueDate: null,
    history: [],
  });

  const blankHistoryObject = {
    name: assignedName,
    start: undefined,
    end: undefined,
    status: "完了"
  }
  // ダイアログの表示を切り替える時にエラーをリセットする
  useEffect(() => {
    if (isOpen === true) {
      setDateTimeError(null);
    }
  }, [isOpen]);

  const [dateTimeError, setDateTimeError] =
    useState<SetStateAction<DateTimeValidationError>>(null);

  useEffect(() => {
    if (mode === "new") {
      //  新規作成する際に利用する
      const newTask = createNewTask();
      setDialogTask({
        ...newTask,
        mode: mode,
        history: newTask.history ?? []
      });
    } else if (mode === "historyEdit") {
      // 履歴編集
      const history = (() => {
        if (task?.history?.length === 0 || !task?.history) return [blankHistoryObject]
        else return task.history
      })()
      setDialogTask({
        ...task,
        taskId: task?.taskId ?? crypto.randomUUID(),
        taskName: task?.taskName ?? "",
        status: task?.status ?? "未着手",
        mode: mode,
        history: history,
      })
    } else {
      // 既存タスクを見る、編集する場合
      setDialogTask({
        ...task,
        status: task?.status ?? "未着手",
        taskId: task?.taskId ?? crypto.randomUUID(),
        scheduledTime: task?.scheduledTime ?? 60,
        taskName: task?.taskName ?? "",
        details: task?.details ?? "",
        category: task?.category ?? "",
        location: task?.location ?? "",
        mode: mode,
        assignedName: task?.assignedName ?? "",
        start: task?.start ?? new Date(),
        end: task?.end ?? new Date(),
        source: task?.source ?? "",
        dueDate: task?.dueDate ?? null,
        history: task?.history ?? [],
      });
    }
  }, [mode, task]);

  const isViewMode = mode === "view";

  const viewModeProps = useMemo(
    () => ({
      slotProps: {
        input: {
          readOnly: isViewMode,
        },
      },
    }),
    [isViewMode]
  );

  const handleSave = async () => {
    try {
      //dialogTaskには、APIに渡すべきではないmodeが含まれているため、ここで分離する
      const { mode: mode, ...taskForBackend } = dialogTask;
      if (!isBaseTask(taskForBackend)) return

      if (mode === "historyEdit") {
        const formattedTask = dataAlignerOnHistoryEdit(taskForBackend)
        await updateTask(formattedTask);

      } else {
        // ステータスが完了の場合はendを現在時刻として送信
        if (dialogTask.status === "完了") {
          taskForBackend.end = new Date();
        }
        const formattedTask = dataAlignerOnEdit(taskForBackend, tasks)
        await updateTask(formattedTask);
      }
      closeDialog();
    } catch (e: unknown) {
      if (e instanceof Error) {
        alert(e.message || "タスク保存に失敗しました");
      } else {
        alert("タスク保存に失敗しました");
      }
    }
  };

  const handleStartNow = () => {
    let remaining_time;
    remaining_time = getRemainingTime(dialogTask);
    if (remaining_time <= 0) remaining_time = 30;

    const new_task = {
      ...dialogTask,
      status: "進行中",
      assignedName: assignedName,
      start: new Date(),
      end: new Date(new Date()?.getTime() + remaining_time * 60 * 1000),
    };
    if (isBaseTask(new_task)) updateTask(new_task);
    setSelectedDate(dayjs(new Date()));
    closeDialog();
  };

  // タスク削除処理。必要になる可能性があるためコメントアウトで残す
  // const handleDelete = () => {
  //   const taskIdToDelete = dialogTask.taskId;
  //   setTasks((prev) => prev.filter((t) => t.taskId !== taskIdToDelete));
  //   setBacklogTasks((prev) => prev.filter((t) => t.taskId !== taskIdToDelete));

  //   closeDialog();
  // };

  //  パターン2で、発信元をアイコン表示する場合に使う
  // const returnSourceIcon = (source: string) => {
  //   if (source === "teams") return <GroupIcon />;
  //   if (source === "インカム") return <GraphicEqIcon />;
  //   return null;
  // };

  const calcWorkedTIme = (start: Date | undefined, end: Date | undefined) => {
    if (start && end) {
      const worked_time = Math.round(
        Math.abs(end.getTime() - start.getTime()) / (1000 * 60)
      );
      return worked_time;
    } else {
      return 0;
    }
  };
  // workedTime = historyの時間合計
  const getWorkedTime = (dialogTask: DialogTask) => {
    if (dialogTask.history) {
      const sum_worked_time = dialogTask.history.reduce(
        (sum, task) => sum + calcWorkedTIme(task.start, task.end),
        0
      );
      return sum_worked_time;
    }
    return 0;
  };
  const getRemainingTime = (dialogTask: DialogTask): number => {
    const worked_time = getWorkedTime(dialogTask);
    if (!dialogTask.scheduledTime) return 0
    if (dialogTask?.scheduledTime != null && worked_time != null) {
      return Math.floor(dialogTask.scheduledTime - worked_time);
    }
    return dialogTask.scheduledTime;
  };

  const onChangeDate = (newValue: dayjs.Dayjs | null, field: string) => {
    const newDialogTask = { ...dialogTask, [field]: newValue ? newValue.toDate() : new Date() }
    setDialogTask(newDialogTask);
    const { mode: mode, ...taskForBackend } = dialogTask;
    if (newValue && isBaseTask(taskForBackend))
      updateTask(taskForBackend);
  }
  const deleteHistory = (index: number) => {
    const tempTask = { ...dialogTask }
    if (tempTask.history) {
      tempTask.history.splice(index, 1)
      setDialogTask(tempTask)
    }
  }
  const addHistory = () => {
    const tempTask = { ...dialogTask }
    if (!tempTask.history) {
      tempTask.history = []
    }
    tempTask.history.push(blankHistoryObject)
    setDialogTask(tempTask)
  }

  if (!isOpen) {
    return null;
  } else {
    return (
      <>
        <Box
          sx={{
            display: "flex",
            justifyContent: "right",
            alignItems: "center",
            mb: 2,
            position: "sticky",
            backgroundColor: "white",
            zIndex: 1,
          }}
        >
          <IconButton onClick={closeDialog}>
            <CloseIcon />
          </IconButton>
        </Box>

        <Stack
          spacing={3}
          sx={{
            margin: 2,
          }}
        >
          {(task?.status == "中断" || task?.status == "未着手") && (
            <Box sx={{ display: "flex", mb: 3 }}>
              <Button
                variant="contained"
                sx={{ backgroundColor: "#DE8289", color: "#FFFFFF" }}
                onClick={handleStartNow}
              >
                今すぐ着手する
              </Button>
            </Box>
          )}
          <Box>
            <TextField
              rows={4}
              label="タスクのタイトル"
              fullWidth
              value={dialogTask?.taskName}
              onChange={(e) =>
                setDialogTask({
                  ...dialogTask,
                  taskId: task?.taskId ?? "",
                  taskName: e.target.value,
                })
              }
              variant="outlined"
              {...viewModeProps}
              disabled={isViewMode}
              size="small"
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TextField
              multiline
              rows={4}
              label="タスク詳細"
              fullWidth
              value={dialogTask?.details}
              onChange={(e) =>
                setDialogTask({ ...dialogTask, details: e.target.value })
              }
              variant="outlined"
              {...viewModeProps}
              disabled={isViewMode}
              size="small"
            />
          </Box>

          {isViewMode && (
            <Box sx={{ display: "flex", alignItems: "baseline", gap: 2 }}>
              <Typography variant="subtitle1">発信元：</Typography>
              {dialogTask.source ? (
                <Link
                  href="about:blank"
                  target="_blank"
                  rel="noopener noreferrer"
                  color="textPrimary"
                  sx={{ fontSize: "default", textOverflow: "ellipsis" }}
                >
                  {dialogTask.source}
                </Link>
              ) : (
                <Typography color="textPrimary" sx={{ fontSize: "default" }}>
                  未確定
                </Typography>
              )}
            </Box>
          )}
          {/* パターン2：アイコン表示
              <Link
                href="about:blank"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ display: "inline-flex" }}
              >
                発信元：{returnSourceIcon(dialogTask.source)}
              </Link>
            </Box>
          )} */}

          {isViewMode && (
            <Box sx={{ display: "flex", alignItems: "baseline", gap: 2 }}>
              <Typography variant="subtitle1">期日：</Typography>
              <Typography color="textPrimary" sx={{ fontSize: "default" }}>
                {dialogTask.dueDate
                  ? convertToDayjs(dialogTask.dueDate)?.format("YYYY/MM/DD")
                  : null}
              </Typography>
            </Box>
          )}

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TextField
              fullWidth
              label="カテゴリ"
              value={dialogTask?.category}
              onChange={(e) =>
                setDialogTask({ ...dialogTask, category: e.target.value })
              }
              variant="outlined"
              {...viewModeProps}
              sx={{ width: "200px" }}
              disabled={isViewMode}
              size="small"
            />
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TextField
              fullWidth
              label="場所"
              value={dialogTask?.location}
              onChange={(e) =>
                setDialogTask({ ...dialogTask, location: e.target.value })
              }
              variant="outlined"
              {...viewModeProps}
              sx={{ width: "200px" }}
              disabled={isViewMode}
              size="small"
            />
          </Box>

          <Box sx={{ gap: 2 }}>
            <Typography component={"div"}>ステータス</Typography>
            <ToggleButtonGroup
              value={dialogTask?.status}
              exclusive
              onChange={(_, newStatus) => {
                if (isViewMode && newStatus) {
                  const { mode: mode, ...taskForBackend } = dialogTask;
                  if (!isBaseTask(taskForBackend)) return
                  setDialogTask({ ...dialogTask, status: newStatus });
                  updateTask({ ...taskForBackend, status: newStatus });
                } else if (newStatus !== null) {
                  setDialogTask({ ...dialogTask, status: newStatus });
                }
              }}
              aria-label="task status"
            >
              <ToggleButton
                value="未着手"
                disabled={dialogTask.status !== "未着手"}
              >
                未着手
              </ToggleButton>
              <ToggleButton value="進行中">進行中</ToggleButton>
              <ToggleButton value="完了">完了</ToggleButton>
              <ToggleButton value="中断">中断</ToggleButton>
            </ToggleButtonGroup>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TextField
              type="number"
              label="作業予定時間（分）"
              value={dialogTask?.scheduledTime}
              onChange={(e) =>
                setDialogTask({
                  ...dialogTask,
                  scheduledTime: Number(e.target.value),
                })
              } //error handling needed
              variant="outlined"
              {...viewModeProps}
              sx={{ width: "200px" }}
              disabled={isViewMode}
              size="small"
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <TextField
              type="number"
              label="作業実績時間（分）"
              value={getWorkedTime(dialogTask)}
              variant="outlined"
              {...viewModeProps}
              sx={{ width: "200px" }}
              size="small"
            />
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Typography variant="subtitle1" sx={{ minWidth: "70px" }}>
              残り時間（分）：
            </Typography>
            <Typography>{getRemainingTime(dialogTask)}分</Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2 }}>
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="ja">
              <Stack spacing={2}>
                <Box>
                  <TextField
                    value={dialogTask.assignedName || ""}
                    label="担当者"
                    onChange={(e) => {
                      setDialogTask((prev) => ({
                        ...prev,
                        assignedName: e.target.value,
                      }));
                    }}
                    variant="outlined"
                    sx={{ width: "200px" }}
                    {...viewModeProps}
                    disabled={isViewMode}
                    size="small"
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <DateTimePicker
                    value={
                      dialogTask.start ? convertToDayjs(dialogTask.start) : null
                    }
                    onChange={(newValue) => onChangeDate(newValue, "start")}
                    disabled={mode !== "new" && mode != "backlog"}
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography>~</Typography>
                  <DateTimePicker
                    value={
                      dialogTask.end ? convertToDayjs(dialogTask.end) : null
                    }
                    onChange={(newValue) => onChangeDate(newValue, "end")}
                    disabled={mode !== "new" && mode != "backlog"}
                  />
                </Box>
              </Stack>
            </LocalizationProvider>
          </Box>
          {(mode === "historyEdit" || (dialogTask?.history?.length !== 0 &&
            dialogTask.history !== undefined)) && (
              <Box
                sx={{
                  position: "relative",
                  border: "solid 1px gray",
                  borderRadius: "4px",
                  padding: "1em 1em 0",
                }}
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: "-11px",
                    left: "10px",
                    background: "#fff",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  作業履歴
                  <AccessTimeIcon />
                </Box>
                {mode === "historyEdit" ? (
                  <Box>
                    {dialogTask?.history?.map((his, index) => (
                      <Box
                        key={dialogTask.taskId + index}
                        sx={{
                          display: "flex",
                          flexDirection: "row",
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "left",
                            marginBottom: 1,
                          }}>
                          <Box>
                            <TextField
                              value={his.name || ""}
                              label="担当者"
                              onChange={(e) => {
                                setDialogTask((prev) => {
                                  const updatedData = { ...prev }
                                  updatedData.history![index] = {
                                    ...prev.history![index],
                                    name: e.target.value
                                  }
                                  return updatedData
                                });
                              }}
                              variant="outlined"
                              sx={{ width: "100px" }}
                              {...viewModeProps}
                              size="small"
                            />
                            <FormControl>
                              <Select
                                labelId="demo-simple-select-label"
                                id="demo-simple-select"
                                value={his.status}
                                label="Age"
                                onChange={(event) => {
                                  const updatedData = { ...dialogTask }
                                  updatedData.history![index] = {
                                    ...dialogTask.history![index],
                                    status: event.target.value,
                                  }
                                  setDialogTask(updatedData);
                                }}
                                size="small"
                              >
                                <MenuItem value={"完了"}>完了</MenuItem>
                                <MenuItem value={"中断"}>中断</MenuItem>
                              </Select>
                            </FormControl>
                          </Box>
                          <Box
                          >
                            <LocalizationProvider
                              dateAdapter={AdapterDayjs}
                              adapterLocale="ja"
                            >
                              <DateTimePicker
                                value={
                                  his.start
                                    ? convertToDayjs(his.start)
                                    : null
                                }
                                onChange={(newValue) => {
                                  const updatedData = { ...dialogTask }
                                  updatedData.history![index] = {
                                    ...dialogTask.history![index],
                                    start: newValue ? newValue.toDate() : undefined,
                                  }
                                  setDialogTask(updatedData);
                                }}
                                disabled={isViewMode}
                                maxDateTime={convertToDayjs(his.end ?? new Date()) ?? undefined}
                                onError={(newError) => setDateTimeError(newError)}
                                sx={{ width: "200px" }}
                              /> ~
                              <DateTimePicker
                                value={
                                  his.end ? convertToDayjs(his.end) : null
                                }
                                onChange={(newValue) => {
                                  const updatedData = dialogTask
                                  updatedData.history![index] = {
                                    ...dialogTask.history![index],
                                    end: newValue ? newValue.toDate() : undefined,
                                  }
                                  setDialogTask(updatedData);
                                }}
                                disabled={isViewMode}
                                minDateTime={convertToDayjs(his.start ?? null) ?? undefined}
                                maxDateTime={convertToDayjs(new Date()) ?? undefined}
                                sx={{ width: "200px" }}
                              />
                            </LocalizationProvider>
                          </Box>

                        </Box>
                        <IconButton aria-label="delete" onClick={() => deleteHistory(index)}>
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    ))
                    }
                    <IconButton aria-label="add" onClick={() => addHistory()}>
                      <AddIcon />

                    </IconButton>
                  </Box>

                ) : (
                  dialogTask?.history?.map((his) => (
                    <Box
                      key={dialogTask.taskId}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginBottom: 1,
                      }}
                    >
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          sx={{ display: "flex", alignItems: "center" }}
                        >
                          {his.name}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          flex: 1,
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                        }}
                      >
                        <Typography sx={{ whiteSpace: "nowrap" }}>
                          {his.start?.toLocaleString()}
                        </Typography>
                        <Typography sx={{ whiteSpace: "nowrap" }}>
                          〜{his.end?.toLocaleString()}
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          flex: 1,
                          display: "flex",
                          justifyContent: "flex-end",
                        }}
                      >
                        <Typography>{his.status}</Typography>
                      </Box>
                    </Box>
                  ))
                )

                }

              </Box>
            )}

          {isViewMode && (
            <>
              {/* <Box sx={{ display: "flex", justifyContent: "left", mt: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setMode("edit")}>
                  編集
                </Button>
              </Box> */}
              <Box sx={{ display: "flex", justifyContent: "left", mt: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setMode("historyEdit")}>
                  履歴編集
                </Button>
              </Box>
            </>
          )}

          {(mode === "new" || mode === "edit" || mode === "historyEdit") && (
            <Box
              sx={{ display: "flex", justifyContent: "left", mt: 2, gap: 2 }}
            >
              <Button variant="contained" color="success" onClick={handleSave}>
                保存
              </Button>
            </Box>
          )}
        </Stack>
      </>
    );
  }
};

export default Dialog;
