import { BaseTask, CardTask, isBaseTask, TaskHistory } from "../types";

const dataAlignerOnHistoryEdit = (newData: BaseTask): BaseTask => {
  const formattedTask = { ...newData };
  const sortedHistory =
    newData.history?.length === 0
      ? undefined
      : newData.history?.sort((a, b) => a.start.getTime() - b.start.getTime());
  formattedTask.status = sortedHistory
    ? sortedHistory[sortedHistory.length - 1].status
    : "未着手";
  return formattedTask;
};

const dataAlignerOnEdit = (newData: BaseTask, tasks: BaseTask[]): BaseTask => {
  const prevTask = tasks?.find((task) => task.taskId == newData.taskId);
  if (
    ((prevTask?.status == "進行中" && newData.status == "中断") ||
      (prevTask?.status != "完了" && newData.status == "完了")) &&
    prevTask?.assignedName &&
    prevTask.start
  ) {
    if (!newData.history) newData.history = [];
    newData.history.push({
      name: prevTask?.assignedName,
      start: prevTask.start,
      end: new Date(),
      status: newData.status,
    });
  }
  return newData;
};

const dataAlignerOnSwipe = (
  newData: CardTask | BaseTask,
  endTime?: Date
): BaseTask => {
  const taskForUpdate = isBaseTask(newData) ? newData : newData.baseTask;
  taskForUpdate.status = "完了";
  const history: TaskHistory = {
    name: taskForUpdate.assignedName!,
    start: taskForUpdate.start!,
    end: endTime ?? new Date(),
    status: "完了",
  };
  if (!taskForUpdate.history) {
    taskForUpdate.history = [history];
  } else {
    taskForUpdate.history.push(history);
  }
  return taskForUpdate;
};

const dataAlignerOnDoubleTap = (newData: CardTask): BaseTask => {
  const taskForUpdate = newData.baseTask;
  const history: TaskHistory = {
    name: taskForUpdate.assignedName!,
    start: taskForUpdate.start!,
    end: new Date(),
    status: "中断",
  };
  taskForUpdate.status = "中断";
  taskForUpdate.assignedName = "";
  taskForUpdate.start = undefined;
  taskForUpdate.end = undefined;
  if (!taskForUpdate.history) {
    taskForUpdate.history = [history];
  } else {
    taskForUpdate.history.push(history);
  }
  return taskForUpdate;
};
export {
  dataAlignerOnHistoryEdit,
  dataAlignerOnEdit,
  dataAlignerOnSwipe,
  dataAlignerOnDoubleTap,
};
