import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useEffect,
  useState,
} from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";

export interface UserContextInterface {
  username: string;
  setUsername: Dispatch<SetStateAction<string>>;
}

const defaultState = {
  username: "",
  setUsername: (() => {}) as Dispatch<SetStateAction<string>>,
} as UserContextInterface;

// eslint-disable-next-line react-refresh/only-export-components
export const UserContext = createContext<UserContextInterface>(defaultState);

interface UserProviderProps {
  children: ReactNode;
}

export default function UserProvider({ children }: UserProviderProps) {
  const [username, setUsername] = useState<string>(() => {
    return localStorage.getItem("username") || "";
  });

  useEffect(() => {
    if (username) {
      localStorage.setItem("username", username);
    } else {
      localStorage.removeItem("username");
    }
  }, [username]);

  const contextValue: UserContextInterface = {
    username,
    setUsername,
  };

  return (
    <UserContext.Provider value={contextValue}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        {children}
      </LocalizationProvider>
    </UserContext.Provider>
  );
}
