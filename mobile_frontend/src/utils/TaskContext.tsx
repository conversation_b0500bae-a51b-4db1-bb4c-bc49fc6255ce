import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { BaseTask, CardTask } from "../types/task";
import axios, { AxiosRequestConfig } from "axios";
import dayjs, { Dayjs } from "dayjs";

const apiConfig: AxiosRequestConfig = {
  headers: { "Content-Type": "application/json" },
};

const getTasks = async (
  area_id: string,
  date: string,
  assigned_name: string
) => {
  apiConfig.params = {
    area_id,
    date,
    assigned_name,
  };
  // If you need to use selectedDate here, pass it as an argument from the caller.
  const res = await axios.get("/api/tasks", apiConfig).then((res) => {
    // 本当はBaseTask型にはならない
    const modifiedData = res.data.tasks?.map((task: BaseTask) => {
      if (task.history && task.history.length > 0) {
        task.history = task.history.map((record) => {
          return {
            ...record,
            start: new Date(record.start),
            end: new Date(record.end),
          };
        });
      }
      const modifiedTask = {
        ...task,
        start: task.start ? new Date(task.start) : undefined,
        end: task.end ? new Date(task.end) : undefined,
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
      };
      return modifiedTask;
    });
    return modifiedData;
    // TODO: GET APIができたら変更する
    // const assigned_name_data = modifiedData.filter((data: BaseTask) => data.assignedName == "田中")
    // return assigned_name_data;
  });
  return res;
};
const putTask = async (task: BaseTask, date: string, assignedName: string) => {
  await axios.put("/api/task", task, apiConfig).catch((err) => {
    throw new Error(`タスク更新に失敗: ${err}`);
  });
  const responseTasks = await getTasks("Test", date, assignedName);
  return responseTasks;
};

const convertToCardTasks = (tasks: BaseTask[]) => {
  const cardTaskList = tasks.flatMap((task: BaseTask) => {
    const returnArray = [] as CardTask[];
    if (task.status !== "完了") {
      returnArray.push({
        cardId: crypto.randomUUID(),
        isHistoryData: false,
        taskId: task.taskId,
        taskName: task.taskName,
        status: task.status,
        category: task.category,
        location: task.location,
        scheduledTime: task.scheduledTime,
        details: task.details,
        source: task.source,
        assignedName: task.assignedName,
        start: task.start,
        end: task.end,
        dueDate: task.dueDate,
        baseTask: task,
      });
    }
    if (task.history && task.history.length > 0) {
      task.history.forEach((record) => {
        returnArray.push({
          cardId: crypto.randomUUID(),
          isHistoryData: true,
          taskId: task.taskId,
          taskName: task.taskName,
          status: record.status,
          category: task.category,
          location: task.location,
          scheduledTime: task.scheduledTime,
          details: task.details,
          source: task.source,
          assignedName: record.name,
          start: record.start,
          end: record.end,
          dueDate: task.dueDate,
          baseTask: task,
        });
      });
    }
    return returnArray;
  });
  const returnValue = {
    initialTasks: [] as CardTask[],
    unassignedcarryoveredTasks: [] as CardTask[],
    // carryoveredTasks: [] as CardTask[]
  };
  cardTaskList.forEach((cardTask: CardTask) => {
    const isScheduled = Boolean(cardTask.start && cardTask.end);
    if (cardTask.isHistoryData || isScheduled) {
      returnValue.initialTasks.push(cardTask);
    } else if (!isScheduled) {
      returnValue.unassignedcarryoveredTasks.push(cardTask);
      // } else if (!isScheduled && cardTask.status !== "中断") {
      //   returnValue.unassignedTasks.push(cardTask)
    } else {
      // 本来上の条件のいずれかにマッチするはずのため、入らない分岐
      console.log(`uncategorized task:${JSON.stringify(cardTask)}`);
    }
  });
  return returnValue;
};

interface TaskContextType {
  tasks: BaseTask[];
  setTasks: React.Dispatch<React.SetStateAction<BaseTask[]>>;
  calendarTasks: CardTask[];
  setCalendarTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  backlogTasks: CardTask[];
  setBacklogTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  carryoverTasks: CardTask[];
  setCarryoverTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  updateTaskTime: (taskId: string, newStart: Date, duration: number) => void;
  refreshTasks: (date: string, assignedName: string) => Promise<void>;
  createTask: (task: BaseTask) => Promise<object>;
  updateTask: (task: BaseTask) => Promise<object>;
  removeTask: (task: BaseTask) => Promise<object>;
  calculateDuration: (start: Date, end: Date) => number;
  formatTime: (date: Date) => string;
  createNewTask: () => BaseTask;
  selectedDate: Dayjs;
  setSelectedDate: React.Dispatch<React.SetStateAction<Dayjs>>;
  assignedName: string;
  setAssignedName: React.Dispatch<React.SetStateAction<string>>;
}

const TaskContext = createContext<TaskContextType | undefined>(undefined);

export const TaskProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [tasks, setTasks] = useState<BaseTask[]>([]);
  const [calendarTasks, setCalendarTasks] = useState<CardTask[]>([]);
  const [backlogTasks, setBacklogTasks] = useState<CardTask[]>([]);
  const [carryoverTasks, setCarryoverTasks] = useState<CardTask[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs("2025-05-02"));
  // TODO：初期値田中で、DailyViewのTextfieldに名前を修正したら、別の人のタスクが表示するようにしていますが、今後はTextField編集不可にして、assignedNameはログイン情報から取得するように変更する？
  const [assignedName, setAssignedName] = useState<string>("田中");
  useEffect(() => {
    const { initialTasks, unassignedcarryoveredTasks } =
      convertToCardTasks(tasks);
    setCalendarTasks(initialTasks);
    setBacklogTasks(unassignedcarryoveredTasks);
  }, [tasks]);

  const updateTaskTime = (id: string, newStart: Date, duration: number) => {
    setTasks((prev) =>
      prev.map((t) =>
        t.taskId === id
          ? {
            ...t,
            start: newStart,
            end: new Date(newStart.getTime() + duration * 60000),
            scheduledTime: duration,
          }
          : t
      )
    );
  };

  const calculateDuration = (start: Date, end: Date) =>
    Math.round((end.getTime() - start.getTime()) / 60000);

  const formatTime = (date: Date) =>
    date.toLocaleTimeString("ja-JP", { hour: "2-digit", minute: "2-digit" });

  const refreshTasks = async (date: string, assignedName: string) => {
    const fetchData = async () => {
      try {
        // assignedNameをグローバルstateから取得
        const response = await getTasks(
          "Test",
          date,
          assignedName
        );
        setTasks(response);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  };
  const updateTask = async (newData: BaseTask) => {
    const refreshedTasks = await putTask(
      newData,
      selectedDate.format("YYYY-MM-DD"),
      assignedName
    );
    setTasks(refreshedTasks);
    return refreshedTasks;
  };
  const createTask = async (newData: BaseTask) => {
    // const refreshedTasks = await postTask(task)
    // setTasks(refreshedTasks)
    // return refreshedTasks
    return updataTaskDataOnlyClient(newData, "create");
  };
  const removeTask = async (newData: BaseTask) => {
    // const refreshedTasks = await deleteTask(newData)
    // setTasks(refreshedTasks)
    // return refreshedTasks
    return updataTaskDataOnlyClient(newData, "remove");
  };
  // APIができるまでの間、クライアント側のStateだけを更新する関数
  const updataTaskDataOnlyClient = (newData: BaseTask, action: string) => {
    const newTaskList = (() => {
      if (action === "update") {
        const newList = tasks.map((task) => {
          if (task.taskId === newData.taskId) {
            return newData;
          } else {
            return task;
          }
        });
        return newList;
      } else if (action === "create") {
        return tasks.concat([newData]);
      } else if (action === "remove") {
        return tasks.filter((task) => task.taskId !== newData.taskId);
      } else {
        return [] as BaseTask[];
      }
    })();
    setTasks(newTaskList);
    return newData;
  };

  // BaseTask型の新規タスクのテンプレを返す関数
  const createNewTask = () => {
    const newTask: BaseTask = {
      taskId: crypto.randomUUID(),
      taskName: "",
      location: "",
      category: "",
      source: "",
      status: "未着手",
      start: undefined,
      end: undefined,
      assignedName: "",
      details: "",
      scheduledTime: 60,
    };

    return newTask;
  };

  return (
    <TaskContext.Provider
      value={{
        tasks,
        setTasks,
        calendarTasks,
        setCalendarTasks,
        backlogTasks,
        setBacklogTasks,
        carryoverTasks,
        setCarryoverTasks,
        updateTaskTime,
        refreshTasks,
        createTask,
        updateTask,
        removeTask,
        calculateDuration,
        formatTime,
        createNewTask,
        selectedDate,
        setSelectedDate,
        assignedName,
        setAssignedName,
      }}
    >
      {children}
    </TaskContext.Provider>
  );
};

export const useTasks = () => {
  const context = useContext(TaskContext);
  if (context === undefined) {
    throw new Error("useTasks must be used within a TaskProvider");
  }
  return context;
};
