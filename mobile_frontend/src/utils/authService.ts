import { BASE_URL } from "./api";

interface UserAttributes {
  sub?: string;
  email?: string;
  [key: string]: string | undefined;
}
interface UserData {
  username: string;
  user_attributes: UserAttributes;
  cognito_username: string;
  session_metadata?: {
    user_agent: string;
    client_ip: string;
    login_time: number;
  };
}
interface LoginResponse {
  message: string;
  username: string;
  user_data: UserData;
}

export const signIn = async (
  username: string,
  password: string,
): Promise<LoginResponse> => {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: "POST",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username,
        password,
      }),
    });

    if (!response.ok) {
      throw new Error("Login failed");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error signing in: ", error);
    throw error;
  }
};
