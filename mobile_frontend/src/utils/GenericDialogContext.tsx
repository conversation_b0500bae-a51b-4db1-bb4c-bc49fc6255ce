import React, { createContext, useContext, useState, ReactNode } from "react";

interface GenericDialogContextType {
  isOpen: boolean;
  content: React.ComponentType | null,
  props: unknown,
  openDialog: (content: React.ComponentType, props: object) => void;
  closeDialog: () => void;
}

const DialogContext = createContext<GenericDialogContextType | undefined>(undefined);

export const GenericDialogProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState<React.ComponentType | null>(null);
  const [props, setProps] = useState<unknown>({});

  const openDialog = (content: React.ComponentType, props: unknown) => {
    setContent(() => content)
    setProps(props)
    setIsOpen(true);
  };

  const closeDialog = () => {
    setIsOpen(false);
  };

  return (
    <DialogContext.Provider
      value={{
        isOpen,
        content,
        props,
        openDialog,
        closeDialog,
      }}>
      {children}
    </DialogContext.Provider>
  );
};

export const useGenericDialog = () => {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error("useDialog must be used within a DialogProvider");
  }
  return context;
};
