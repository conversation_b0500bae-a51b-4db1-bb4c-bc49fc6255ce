// import { useState } from "react";
import Router from "./routes";
import "./index.css";
import UserProvider from "./utils/AccountContext";
import { TaskProvider } from "./utils/TaskContext";
import { DialogProvider } from "./utils/DialogContext";
import { GenericDialogProvider } from "./utils/GenericDialogContext";
import { createTheme, ThemeProvider } from "@mui/material/styles";

const theme = createTheme({
  typography: {
    fontSize: 12,
  }
});

const App = () => {
  // const [username, setUsername] = useState("");
  return (
    <ThemeProvider theme={theme}>
      <UserProvider>
        <TaskProvider>
          <DialogProvider>
            <GenericDialogProvider>
              <Router />
            </GenericDialogProvider>
          </DialogProvider>
        </TaskProvider>
      </UserProvider>

    </ThemeProvider>
  );
};

export default App;
