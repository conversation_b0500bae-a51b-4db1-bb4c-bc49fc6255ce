# 作業カンバン

## 導入要件

- [Node.js](https://nodejs.org/en) v20.18.0
- [Python](https://www.python.org/downloads/windows/) v3.12.7
- [Rancher Desktop](https://docs.rancherdesktop.io/getting-started/installation/) v1.17.1
- [AWSを使用するための事前準備](https://ibm.ent.box.com/file/1782695483032?s=********************************)


## フロントエンド

### ローカルでの起動方法

```bash
$ cd ./frontend
$ npm install
$ npm run dev
```

`http://localhost:3000/`で動作確認

### コンテナでの起動方法

docker コンテナ作成

```bash
$ cd ./frontend
$ docker build -t frontend .
```

docker 実行

```bash
$ docker run -d --name frontend -p 3000:3000 -v ${PWD}:/app -v /app/node_modules frontend
```

`http://localhost:3000/`で動作確認

## バックエンド

### コンテナでの起動方法

#### docker-compose を使った起動方法
env_templateをコピーして.envファイルを作成する。
MFA_SERIAL_NUMBERに、AWSのIAMからMFAデバイスの識別子を参照して入力する。


```bash
$ cd ./backend
$ docker-compose run --rm api python init_mfa.py
$ docker-compose up
```

#### Dockerfile を使った起動方法

docker コンテナ作成

```bash
$ cd ./backend
$ docker build -t backend .
```

docker 実行

```bash
$ docker run -d --name backend -p 8000:8000 -v .\app:/code/app backend
```

`localhost:8000/docs`で動作確認

テスト実行

```bash
$ docker exec -it backend pytest
```

### バックエンドアプリを ECR へ push する（win の場合は git bash 上で実行）

```bash
$ chmod +x push-to-isecloud.sh
$ docker-compose run --rm api python init_mfa.py
$ ./push-to-isecloud.sh <mfa code>
```
