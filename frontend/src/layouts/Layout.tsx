import { Box } from "@mui/material";
import Navbar from "../components/Navbar";
import { useLocation } from "react-router-dom";
// import Sidebar from "../components/Sidebar";
// import { useRef, useState } from "react";

type LayoutProps = {
  children: React.ReactNode;
  title: string;
};

const Layout: React.FC<LayoutProps> = ({ children, title }) => {
  // const drawerRef = useRef<{ isOpen: boolean }>({ isOpen: false });
  // // const [isOpen, setIsOpen] = useState(false);

  // const handleDrawerToggle = () => {
  //   if (drawerRef.current) {
  //     drawerRef.current.isOpen = !drawerRef.current.isOpen;
  //     setIsOpen(drawerRef.current.isOpen);
  //   }
  // };
  const location = useLocation();
  const isTaskPage = location.pathname === "/tasks";

  return (
    <>
      <Navbar
        title={title}
        showBackButton={isTaskPage}
      />
      <Box>
        <Box component="main" sx={{ mt: 10 }}>
          {children}
        </Box>
      </Box>
    </>
  );
};
export default Layout;
