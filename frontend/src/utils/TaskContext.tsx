import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import axios, { AxiosRequestConfig } from "axios";
import dayjs, { Dayjs } from "dayjs";
import { BaseTask, CardTask } from "../types";

const apiConfig: AxiosRequestConfig = {
  headers: { "Content-Type": "application/json" },
};
const getTasks = async (area_id: string, date: string) => {
  apiConfig.params = {
    area_id,
    date,
  };
  // If you need to use selectedDate here, pass it as an argument from the caller.
  const res = await axios.get("/api/tasks", apiConfig).then((res) => {
    // 本当はBaseTask型にはならない
    const modifiedData = res.data.tasks?.map((task: BaseTask) => {
      if (task.history && task.history.length > 0) {
        task.history = task.history.map((record) => {
          return {
            ...record,
            start: new Date(record.start),
            end: new Date(record.end),
          };
        });
      }
      const modifiedTask = {
        ...task,
        start: task.start ? new Date(task.start) : undefined,
        end: task.end ? new Date(task.end) : undefined,
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
      };
      return modifiedTask;
    });
    return modifiedData;
  });
  return res;
};

const postTask = async (task: BaseTask, date: string) => {
  await axios.post("/api/tasks", task, apiConfig).catch((err) => {
    throw new Error(`タスク作成に失敗: ${err}`);
  });
  const responseTasks = await getTasks("Test", date);
  return responseTasks;
};
const putTask = async (task: BaseTask, date: string) => {
  await axios.put("/api/task", task, apiConfig).catch((err) => {
    throw new Error(`タスク更新に失敗: ${err}`);
  });
  const responseTasks = await getTasks("Test", date);
  return responseTasks;
};
const deleteTask = async (task: BaseTask, date: string) => {
  const task_id = task.taskId;
  apiConfig.params = {
    task_id,
  };
  await axios.delete("/api/task", apiConfig).catch((err) => {
    throw new Error(`タスク削除に失敗: ${err}`);
  });
  const responseTasks = await getTasks("Test", date);
  return responseTasks;
};

const getWeeklyTasks = async (
  area_id: string,
  start_date: string,
  end_date: string,
) => {
  apiConfig.params = {
    area_id,
    start_date,
    end_date,
  };
  // If you need to use selectedDate here, pass it as an argument from the caller.
  const res = await axios.get("/api/weeklytasks", apiConfig).then((res) => {
    // 本当はBaseTask型にはならない
    const modifiedData = res.data.tasks?.map((task: BaseTask) => {
      if (task.history && task.history.length > 0) {
        task.history = task.history.map((record) => {
          return {
            ...record,
            start: new Date(record.start),
            end: new Date(record.end),
          };
        });
      }
      const modifiedTask = {
        ...task,
        start: task.start ? new Date(task.start) : undefined,
        end: task.end ? new Date(task.end) : undefined,
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
      };
      return modifiedTask;
    });
    return modifiedData;
  });
  return res;
};

const isShowScreen = (
  targetStart: Date | null | undefined,
  targetEnd: Date | null | undefined,
  selectedDate: Dayjs,
) => {
  if (
    (!targetStart || targetStart == null) &&
    (!targetEnd || targetEnd == null)
  )
    return true;

  const start = selectedDate
    .subtract(1, "day")
    .hour(21)
    .minute(0)
    .second(0)
    .millisecond(0);
  const end = selectedDate
    .add(1, "day")
    .hour(9)
    .minute(0)
    .second(0)
    .millisecond(0);

  const target_start = dayjs(targetStart);
  const target_end = dayjs(targetEnd);

  return !(
    target_end.isBefore(start) ||
    target_end.isSame(start) ||
    target_start.isAfter(end) ||
    target_start.isSame(end)
  );
};

const isShowWeeklyScreen = (
  targetStart: Date | null | undefined,
  targetEnd: Date | null | undefined,
  selectedStart: Dayjs,
  selecteEnd: Dayjs,
) => {
  if (
    (!targetStart || targetStart == null) &&
    (!targetEnd || targetEnd == null)
  )
    return true;

  const start = selectedStart.hour(0).minute(0).second(0).millisecond(0);
  const end = selecteEnd.add(1, "day").startOf("day");

  const target_start = dayjs(targetStart);
  const target_end = dayjs(targetEnd);

  return !(
    target_end.isBefore(start) ||
    target_end.isSame(start) ||
    target_start.isAfter(end) ||
    target_start.isSame(end)
  );
};

const convertToCardTasks = (tasks: BaseTask[], selectedDate: Dayjs) => {
  const cardTaskList = tasks.flatMap((task: BaseTask) => {
    const returnArray = [] as CardTask[];
    if (
      task.status !== "完了" &&
      isShowScreen(task.start, task.end, selectedDate)
    ) {
      returnArray.push({
        cardId: crypto.randomUUID(),
        isHistoryData: false,
        taskId: task.taskId,
        taskName: task.taskName,
        status: task.status,
        category: task.category,
        location: task.location,
        scheduledTime: task.scheduledTime,
        details: task.details,
        source: task.source,
        assignedName: task.assignedName,
        start: task.start,
        end: task.end,
        dueDate: task.dueDate,
        baseTask: task,
      });
    }
    if (task.history && task.history.length > 0) {
      task.history.forEach((record) => {
        if (isShowScreen(record.start, record.end, selectedDate)) {
          returnArray.push({
            cardId: crypto.randomUUID(),
            isHistoryData: true,
            taskId: task.taskId,
            taskName: task.taskName,
            status: record.status,
            category: task.category,
            location: task.location,
            scheduledTime: task.scheduledTime,
            details: task.details,
            source: task.source,
            assignedName: record.name,
            start: record.start,
            end: record.end,
            dueDate: task.dueDate,
            baseTask: task,
          });
        }
      });
    }
    return returnArray;
  });
  const returnValue = {
    initialTasks: [] as CardTask[],
    unassignedTasks: [] as CardTask[],
    carryoveredTasks: [] as CardTask[],
  };
  const sortedCardTaskList = [...cardTaskList].sort((a, b) => {
    const scoreA =
      a.end && a.end !== null && a.start && a.start !== null
        ? a.end.getTime() - a.start.getTime()
        : -Infinity;
    const scoreB =
      b.end && b.end !== null && b.start && b.start !== null
        ? b.end.getTime() - b.start.getTime()
        : -Infinity;
    return scoreB - scoreA;
  });
  sortedCardTaskList.forEach((cardTask: CardTask) => {
    const isScheduled = Boolean(
      cardTask.start && cardTask.end && cardTask.assignedName,
    );
    if (cardTask.isHistoryData || isScheduled) {
      returnValue.initialTasks.push(cardTask);
    } else if (!isScheduled && cardTask.status === "中断") {
      returnValue.carryoveredTasks.push(cardTask);
    } else if (!isScheduled && cardTask.status === "未着手") {
      returnValue.unassignedTasks.push(cardTask);
    } else {
      // 本来上の条件のいずれかにマッチするはずのため、入らない分岐
      console.log(`uncategorized task:${JSON.stringify(cardTask)}`);
    }
  });
  return returnValue;
};

const convertToCardWeeklyTasks = (
  tasks: BaseTask[],
  start_date: Dayjs,
  end_date: Dayjs,
) => {
  const cardTaskList = tasks.flatMap((task: BaseTask) => {
    const returnArray = [] as CardTask[];
    if (
      task.status !== "完了" &&
      isShowWeeklyScreen(task.start, task.end, start_date, end_date)
    ) {
      returnArray.push({
        cardId: crypto.randomUUID(),
        isHistoryData: false,
        taskId: task.taskId,
        taskName: task.taskName,
        status: task.status,
        category: task.category,
        location: task.location,
        scheduledTime: task.scheduledTime,
        details: task.details,
        source: task.source,
        assignedName: task.assignedName,
        start: task.start,
        end: task.end,
        dueDate: task.dueDate,
        baseTask: task,
      });
    }
    if (task.history && task.history.length > 0) {
      task.history.forEach((record) => {
        if (
          isShowWeeklyScreen(record.start, record.end, start_date, end_date)
        ) {
          returnArray.push({
            cardId: crypto.randomUUID(),
            isHistoryData: true,
            taskId: task.taskId,
            taskName: task.taskName,
            status: record.status,
            category: task.category,
            location: task.location,
            scheduledTime: task.scheduledTime,
            details: task.details,
            source: task.source,
            assignedName: record.name,
            start: record.start,
            end: record.end,
            dueDate: task.dueDate,
            baseTask: task,
          });
        }
      });
    }
    return returnArray;
  });
  const weeklyCalendarTasks = [] as CardTask[];
  const sortedCardTaskList = [...cardTaskList].sort((a, b) => {
    const scoreA =
      a.end && a.end !== null && a.start && a.start !== null
        ? a.end.getTime() - a.start.getTime()
        : -Infinity;
    const scoreB =
      b.end && b.end !== null && b.start && b.start !== null
        ? b.end.getTime() - b.start.getTime()
        : -Infinity;
    return scoreB - scoreA;
  });
  sortedCardTaskList.forEach((cardTask: CardTask) => {
    const isScheduled = Boolean(
      cardTask.start && cardTask.end && cardTask.assignedName,
    );
    if (cardTask.isHistoryData || isScheduled) {
      weeklyCalendarTasks.push(cardTask);
    }
  });
  return weeklyCalendarTasks;
};

interface TaskContextType {
  tasks: BaseTask[];
  setTasks: React.Dispatch<React.SetStateAction<BaseTask[]>>;
  weeklyTasks: CardTask[];
  setWeeklyTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  calendarTasks: CardTask[];
  setCalendarTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  backlogTasks: CardTask[];
  setBacklogTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  carryoverTasks: CardTask[];
  setCarryoverTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  updateTaskTime: (taskId: string, newStart: Date, duration: number) => void;
  refreshTasks: (date: string) => Promise<void>;
  refreshWeeklyTasks: (start_date: string, end_date: string) => Promise<void>;
  createTask: (task: BaseTask) => Promise<object>;
  updateTask: (task: BaseTask) => Promise<object>;
  removeTask: (task: BaseTask) => Promise<object>;
  calculateDuration: (start: Date, end: Date) => number;
  formatTime: (date: Date) => string;
  createNewTask: () => BaseTask;
  selectedDate: Dayjs;
  setSelectedDate: React.Dispatch<React.SetStateAction<Dayjs>>;
  weeklyTabValue: number;
  setWeeklyTabValue: React.Dispatch<React.SetStateAction<number>>;
}

const TaskContext = createContext<TaskContextType | undefined>(undefined);

export const TaskProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [tasks, setTasks] = useState<BaseTask[]>([]);
  const [weeklyTasks, setWeeklyTasks] = useState<CardTask[]>([]);
  const [calendarTasks, setCalendarTasks] = useState<CardTask[]>([]);
  const [backlogTasks, setBacklogTasks] = useState<CardTask[]>([]);
  const [carryoverTasks, setCarryoverTasks] = useState<CardTask[]>([]);
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs("2025-05-02"));
  const [weeklyTabValue, setWeeklyTabValue] = useState<number>(0);
  useEffect(() => {
    const { initialTasks, unassignedTasks, carryoveredTasks } =
      convertToCardTasks(tasks, selectedDate);
    setCalendarTasks(initialTasks);
    setBacklogTasks(unassignedTasks);
    setCarryoverTasks(carryoveredTasks);
  }, [tasks]);

  const updateTaskTime = (id: string, newStart: Date, duration: number) => {
    setTasks((prev) =>
      prev.map((t) =>
        t.taskId === id
          ? {
              ...t,
              start: newStart,
              end: new Date(newStart.getTime() + duration * 60000),
              scheduledTime: duration,
            }
          : t,
      ),
    );
  };

  const calculateDuration = (start: Date, end: Date) =>
    Math.round((end.getTime() - start.getTime()) / 60000);

  const formatTime = (date: Date) =>
    date.toLocaleTimeString("ja-JP", { hour: "2-digit", minute: "2-digit" });

  const refreshTasks = async (date: string) => {
    const fetchData = async () => {
      try {
        // assignedNameをグローバルstateから取得
        const response = await getTasks("Test", date);
        setTasks(response);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  };
  const updateTask = async (newData: BaseTask) => {
    // 更新した瞬間の画面表示用にclient側でtasksを更新します
    updataTaskDataOnlyClient(newData, "update");
    const refreshedTasks = await putTask(
      newData,
      selectedDate.format("YYYY-MM-DD"),
    );
    setTasks(refreshedTasks);
    return refreshedTasks;
  };
  const updataTaskDataOnlyClient = (newData: BaseTask, action: string) => {
    const newTaskList = (() => {
      if (action === "update") {
        const newList = tasks.map((task) => {
          if (task.taskId === newData.taskId) {
            return newData;
          } else {
            return task;
          }
        });
        return newList;
      } else if (action === "create") {
        return tasks.concat([newData]);
      } else if (action === "remove") {
        return tasks.filter((task) => task.taskId !== newData.taskId);
      } else {
        return [] as BaseTask[];
      }
    })();
    setTasks(newTaskList);
    return newData;
  };
  const createTask = async (newData: BaseTask) => {
    const refreshedTasks = await postTask(
      newData,
      selectedDate.format("YYYY-MM-DD"),
    );
    setTasks(refreshedTasks);
    return refreshedTasks;
  };
  const removeTask = async (newData: BaseTask) => {
    const refreshedTasks = await deleteTask(
      newData,
      selectedDate.format("YYYY-MM-DD"),
    );
    setTasks(refreshedTasks);
    return refreshedTasks;
  };

  // BaseTask型の新規タスクのテンプレを返す関数
  const createNewTask = () => {
    const newTask: BaseTask = {
      taskId: crypto.randomUUID(),
      taskName: "",
      location: "",
      category: "",
      source: "",
      status: "未着手",
      start: undefined,
      end: undefined,
      assignedName: "",
      details: "",
      scheduledTime: 60,
    };

    return newTask;
  };

  const refreshWeeklyTasks = async (start_date: string, end_date: string) => {
    const fetchData = async () => {
      try {
        // assignedNameをグローバルstateから取得
        const response = await getWeeklyTasks("Test", start_date, end_date);
        console.log("response", response);
        const convert_tasks = convertToCardWeeklyTasks(
          response,
          dayjs(start_date),
          dayjs(end_date),
        );
        setWeeklyTasks(convert_tasks);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  };

  return (
    <TaskContext.Provider
      value={{
        tasks,
        setTasks,
        weeklyTasks,
        setWeeklyTasks,
        calendarTasks,
        setCalendarTasks,
        backlogTasks,
        setBacklogTasks,
        carryoverTasks,
        setCarryoverTasks,
        updateTaskTime,
        refreshTasks,
        refreshWeeklyTasks,
        createTask,
        updateTask,
        removeTask,
        calculateDuration,
        formatTime,
        createNewTask,
        selectedDate,
        setSelectedDate,
        weeklyTabValue,
        setWeeklyTabValue,
      }}
    >
      {children}
    </TaskContext.Provider>
  );
};

export const useTasks = () => {
  const context = useContext(TaskContext);
  if (context === undefined) {
    throw new Error("useTasks must be used within a TaskProvider");
  }
  return context;
};
