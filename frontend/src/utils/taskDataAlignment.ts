import { BaseTask } from "../types";

const dataAlignerOnHistoryEdit = (newData: BaseTask): BaseTask => {
  const formattedTask = { ...newData };
  const sortedHistory =
    newData.history?.length === 0
      ? undefined
      : newData.history?.sort((a, b) => a.start.getTime() - b.start.getTime());
  formattedTask.status = sortedHistory
    ? sortedHistory[sortedHistory.length - 1].status
    : "未着手";
  return formattedTask;
};

const dataAlignerOnEdit = (newData: BaseTask, tasks: BaseTask[]): BaseTask => {
  const prevTask = tasks?.find((task) => task.taskId == newData.taskId);
  if (
    ((prevTask?.status == "進行中" && newData.status == "中断") ||
      (prevTask?.status != "完了" && newData.status == "完了")) &&
    prevTask?.assignedName &&
    prevTask.start
  ) {
    if (!newData.history) newData.history = [];
    newData.history.push({
      name: prevTask?.assignedName,
      start: prevTask.start,
      end: new Date(),
      status: newData.status,
    });
  }
  return newData;
};
export { dataAlignerOnHistoryEdit, dataAlignerOnEdit };
