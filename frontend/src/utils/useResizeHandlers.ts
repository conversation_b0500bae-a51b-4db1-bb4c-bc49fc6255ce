import { useState } from "react";
import { BaseTask } from "../types";
import { useTasks } from "./TaskContext";

interface resizeHandlersProps {
  item: BaseTask;
  sxWidth: number | string;
  sxLeft: number;
  hourWidth: number;
}

// リサイズ時の処理（mousemoveで発火）: 見た目だけ変える（State更新のみ、API呼ばない）
export const useResizeHandlers = ({
  item,
  sxWidth,
  sxLeft,
  hourWidth,
}: resizeHandlersProps) => {
  const [isResizing, setIsResizing] = useState<"left" | "right" | null>(null); // どちらのハンドルか
  const [startX, setStartX] = useState(0); // マウスダウンのX座標
  const [originalWidth, setOriginalWidth] = useState(0); // 開始時の幅
  const [originalLeft, setOriginalLeft] = useState(0); // 開始時のleft
  const [resizeOrigin, setResizeOrigin] = useState<{ start: Date; end: Date }>({
    start: item.start ?? new Date(),
    end: item.end ?? new Date(),
  }); // 開始時の時間
  const [localWidth, setLocalWidth] = useState<string | number>(sxWidth);
  const [localLeft, setLocalLeft] = useState(
    typeof sxLeft === "number" ? sxLeft : 0
  );

  const { updateTaskTime } = useTasks();

  const handleResizeStart = (e: React.MouseEvent, side: "left" | "right") => {
    e.stopPropagation(); // D&Dイベント伝播を抑止
    setIsResizing(side);
    setStartX(e.clientX); // 現在のマウス座標
    setOriginalWidth(typeof localWidth === "number" ? localWidth : 200);
    setOriginalLeft(typeof localLeft === "number" ? localLeft : 0);
    setResizeOrigin({
      start: item.start ?? new Date(),
      end: item.end ?? new Date(),
    }); // 元のstart/endを保存
  };

  const handleResizeMove = (e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - startX;
    // 型安全な数値変換
    const baseOriginalWidth =
      typeof originalWidth === "number" ? originalWidth : 200;
    const baseOriginalLeft =
      typeof originalLeft === "number" ? originalLeft : 0;
    let newWidth = baseOriginalWidth;
    let newLeft = baseOriginalLeft;

    // 右側ハンドル
    if (isResizing === "right") {
      newWidth = baseOriginalWidth + deltaX;
    }
    // 左側ハンドル
    else if (isResizing === "left") {
      newWidth = baseOriginalWidth - deltaX;
      newLeft = baseOriginalLeft + deltaX;
    }

    // 幅を最低20px以上に
    newWidth = Math.max(20, newWidth);

    // 15分単位で丸める（hourWidth=100で15分ごと=25pxずつ）
    const quarterHours = Math.round((newWidth / hourWidth) * 4);
    newWidth = (quarterHours / 4) * hourWidth;

    // 左ハンドル時はleftも15分単位にそろえる
    if (isResizing === "left") {
      const leftQuarterHours = Math.round((newLeft / hourWidth) * 4);
      newLeft = (leftQuarterHours / 4) * hourWidth;
    }

    setLocalWidth(newWidth);
    setLocalLeft(newLeft);
  };

  const handleResizeEnd = () => {
    if (!isResizing) return;

    // pixel幅 → 時間変換
    const minutesPerPixel = 60 / hourWidth;
    let newStart = new Date(resizeOrigin.start);
    let newEnd = new Date(resizeOrigin.end);

    // 幅調整前の状態
    console.log("[ResizeEnd] before", {
      isResizing,
      resizeOrigin,
      localLeft,
      localWidth,
      originalLeft,
      originalWidth,
      hourWidth,
      minutesPerPixel,
    });

    if (isResizing === "left") {
      {
        const baseLocalLeft = typeof localLeft === "number" ? localLeft : 0;
        const baseOriginalLeft =
          typeof originalLeft === "number" ? originalLeft : 0;
        const pixelDelta = baseLocalLeft - baseOriginalLeft;
        const minutesDelta = pixelDelta * minutesPerPixel;
        // 5分単位で丸め
        const roundedMinutes = Math.round(minutesDelta / 5) * 5;
        newStart = new Date(
          resizeOrigin.start.getTime() + roundedMinutes * 60 * 1000
        );
        //左侧の変更確認
        console.log("[ResizeEnd] left", {
          baseLocalLeft,
          baseOriginalLeft,
          pixelDelta,
          minutesDelta,
          roundedMinutes,
          newStart: newStart.toISOString(),
          newEnd: newEnd.toISOString(),
        });
        // 開始が終了を超えたら終了時間に揃える（不正防止）
        if (newStart >= newEnd) {
          newStart = new Date(newEnd.getTime() - 15 * 60 * 1000); // 最低15分
        }
      }
    } else if (isResizing === "right") {
      const baseLocalWidth = typeof localWidth === "number" ? localWidth : 200;
      const baseOriginalWidth =
        typeof originalWidth === "number" ? originalWidth : 200;
      const pixelDelta = baseLocalWidth - baseOriginalWidth;
      const minutesDelta = pixelDelta * minutesPerPixel;
      const roundedMinutes = Math.round(minutesDelta / 5) * 5;
      newEnd = new Date(
        resizeOrigin.end.getTime() + roundedMinutes * 60 * 1000
      );
      // 右侧の幅調整の変数
      console.log("[ResizeEnd] right", {
        baseLocalWidth,
        baseOriginalWidth,
        pixelDelta,
        minutesDelta,
        roundedMinutes,
        newStart: newStart.toISOString(),
        newEnd: newEnd.toISOString(),
      });
      // 終了が開始を下回った場合は最低15分後に
      if (newEnd <= newStart) {
        newEnd = new Date(newStart.getTime() + 15 * 60 * 1000);
      }
    }

    // duration計算
    const duration = (newEnd.getTime() - newStart.getTime()) / (60 * 1000);
    // 幅調整の最終結果
    console.log("[ResizeEnd] after", {
      newStart: newStart.toISOString(),
      newEnd: newEnd.toISOString(),
      duration,
    });
    // 本体のステート更新
    updateTaskTime(item.taskId, newStart, duration);

    setIsResizing(null);
  };

  return {
    setLocalWidth,
    setLocalLeft,
    isResizing,
    startX,
    originalWidth,
    originalLeft,
    localLeft,
    localWidth,
    updateTaskTime,
    resizeOrigin,
    handleResizeStart,
    handleResizeMove,
    handleResizeEnd,
  };
};
