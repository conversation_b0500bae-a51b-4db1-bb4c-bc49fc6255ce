import { STORE_MAPPING } from "../data/store_mapping";

export const normalizeStoreName = (store: string): string => {
  if (!store) return "";

  // 余計な記号やスペースがないことを確認
  const trimmedStore = store.trim();

  // 全店舗の場合
  if (trimmedStore.includes("全店") || trimmedStore.includes("通常")) {
    return trimmedStore;
  }

  // Case 1: 店舗コードのみの場合
  if (/^\d+$/.test(trimmedStore)) {
    const normalizedNumber = trimmedStore.replace(/^0+/, "");
    if (STORE_MAPPING[normalizedNumber]) {
      return STORE_MAPPING[normalizedNumber];
    }
    return trimmedStore;
  }

  // Case 2: 店舗コード+店舗名の場合 (e.g. 391青葉台店)
  const numberAndNameMatch = trimmedStore.match(/^\d+(.*)/); //return ["391青葉台店", "青葉台店"]
  if (numberAndNameMatch && numberAndNameMatch[1]) {
    const storeName = numberAndNameMatch[1].trim();
    return storeName;
  }

  // Case 3: 店舗名のみの場合
  return trimmedStore.replace(/店$/, "");
};
