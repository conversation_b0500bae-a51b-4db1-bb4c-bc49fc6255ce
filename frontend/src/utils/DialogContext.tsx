import React, { createContext, useContext, useState, ReactNode } from "react";
import { BaseTask, DialogMode } from "../types/task";

interface DialogContextType {
  isOpen: boolean;
  mode: DialogMode;
  task: BaseTask | undefined;
  openDialog: (mode: DialogMode, task?: BaseTask) => void;
  closeDialog: () => void;
  setMode: (mode: DialogMode) => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

export const DialogProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mode, setMode] = useState<DialogMode>("view");
  const [task, setTask] = useState<BaseTask | undefined>(undefined);

  const openDialog = (newMode: DialogMode, newTask?: BaseTask) => {
    setMode(newMode);
    setTask(newTask);
    setIsOpen(true);
  };

  const closeDialog = () => {
    setIsOpen(false);
  };

  return (
    <DialogContext.Provider
      value={{
        isOpen,
        mode,
        task,
        openDialog,
        closeDialog,
        setMode,
      }}
    >
      {children}
    </DialogContext.Provider>
  );
};

export const useDialog = () => {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error("useDialog must be used within a DialogProvider");
  }
  return context;
};
