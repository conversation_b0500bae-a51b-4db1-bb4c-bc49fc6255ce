import "../App.css";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import { DragOverlay } from "@dnd-kit/core";
import { BaseTask, CardTask, DragData } from "../types";
import TaskCard from "./TaskCard";
import DroppableArea from "./Droppable";
import { useTasks } from "../utils/TaskContext";
import { useDialog } from "../utils/DialogContext";

type BackLogProps = {
  tasks: CardTask[];
  hourWidth: number;
  functions: backlogComponentFunctions;
};
type backlogComponentFunctions = {
  activeItemState: {
    activeBacklogItem: DragData | null;
    setActiveBacklogItem: React.Dispatch<React.SetStateAction<DragData | null>>;
  };
  draggingState: {
    isDragging: boolean;
    setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;
  };
  // handleNewTask: () => void;
  handleTaskClick: (dialogData: BaseTask) => void;
};
export default function BackLog({ tasks, functions }: BackLogProps) {
  const { createNewTask } = useTasks();
  const { openDialog } = useDialog();

  const handleNewTask = () => {
    const newTask = createNewTask();
    openDialog("new", newTask);
  };

  return (
    <DroppableArea id={"backlog"}>
      {(setNodeRef) => (
        <Box
          ref={setNodeRef}
          sx={{
            margin: 1,
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            position: "relative",
            // height: "100%",
            width: "100%",
          }}
        >
          <Typography variant="h5">未割当作業</Typography>
          <Box>
            <Box
              sx={{
                border: "1px gray solid",
                flexDirection: "column",
                flexGrow: 1,
                padding: 1,
                mt: 1,
                overflowY: "scroll",
                height: "calc(80vh - 300px)",
              }}
            >
              {tasks.map((item) => {
                if (
                  functions.draggingState.isDragging &&
                  functions.activeItemState.activeBacklogItem?.item.taskId ===
                  item.taskId
                )
                  return null;
                return (
                  <Box
                    sx={{
                      position: "relative",
                      width: "100%",
                      height: 70,
                      mb: 1,
                    }}
                    key={item.cardId}
                  >
                    <TaskCard
                      item={item}
                      sxLeft={0}
                      sxWidth={"100%"}
                      sxHeight={70}
                      onTaskClick={functions.handleTaskClick}
                      isOverlay={false}
                      from={"backlog"}
                    />
                  </Box>
                );
              })}
              <DragOverlay dropAnimation={null}>
                {functions.activeItemState.activeBacklogItem && (
                  <Box
                    sx={{
                      position: "fixed",
                      width: "100%",
                      height: 70,
                      mb: 1,
                      zIndex: 9999,
                    }}
                  >
                    <TaskCard
                      item={functions.activeItemState.activeBacklogItem?.item}
                      sxLeft={0}
                      sxWidth={"100%"}
                      sxHeight={70}
                      onTaskClick={functions.handleTaskClick}
                      isOverlay={true}
                      from={"backlog"}
                    />
                  </Box>
                )}
              </DragOverlay>
            </Box>
            <Box
              sx={{
                position: "relative",
                height: "auto",
                padding: 1,
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Button
                variant="contained"
                color="primary"
                sx={{
                  width: "15%",
                  boxShadow: 3,
                }}
                // onClick={functions.handleNewTask}
                onClick={handleNewTask}
              >
                追加
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </DroppableArea>
  );
}
