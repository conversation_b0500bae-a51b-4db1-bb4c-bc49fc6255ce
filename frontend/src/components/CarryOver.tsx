import "../App.css";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import { DragOverlay } from "@dnd-kit/core";
import { BaseTask, CardTask, DragData } from "../types";
import TaskCard from "./TaskCard";
import DroppableArea from "./Droppable";

type BackLogProps = {
  tasks: CardTask[] | undefined;
  hourWidth: number;
  functions: backlogComponentFunctions;
};
type backlogComponentFunctions = {
  activeItemState: {
    activeBacklogItem: DragData | null;
    setActiveBacklogItem: React.Dispatch<React.SetStateAction<DragData | null>>;
  };
  draggingState: {
    isDragging: boolean;
    setIsDragging: React.Dispatch<React.SetStateAction<boolean>>;
  };
  // handleNewTask: () => void;
  handleTaskClick: (dialogData: BaseTask) => void;
};
export default function CarryOver({ tasks, functions }: BackLogProps) {

  return (
    <DroppableArea id={"carryover"}>
      {(setNodeRef) => (
        <Box
          ref={setNodeRef}
          sx={{
            margin: 1,
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            position: "relative",
            // height: "100%",
            width: "100%",
          }}>
          <Typography variant="h5">繰越作業</Typography>
          <Box>
            <Box
              sx={{
                border: "1px gray solid",
                flexDirection: "column",
                flexGrow: 1,
                padding: 1,
                mt: 1,
                overflowY: "scroll",
                height: "calc(80vh - 300px)",
              }}>
              {tasks?.map((item) => {
                if (
                  functions.draggingState.isDragging &&
                  functions.activeItemState.activeBacklogItem?.item.cardId ===
                  item.cardId
                )
                  return null;
                return (
                  <Box
                    sx={{
                      position: "relative",
                      width: "100%",
                      height: 70,
                      mb: 1,
                    }}
                    key={item.cardId}>
                    <TaskCard
                      item={item}
                      sxLeft={0}
                      sxWidth={"100%"}
                      sxHeight={70}
                      onTaskClick={functions.handleTaskClick}
                      isOverlay={false}
                      from={"carryover"}
                    />
                  </Box>
                );
              })}
              <DragOverlay dropAnimation={null}>
                {functions.activeItemState.activeBacklogItem && (
                  <Box
                    sx={{
                      position: "fixed",
                      width: "100%",
                      height: 70,
                      mb: 1,
                      zIndex: 9999,
                    }}>
                    <TaskCard
                      item={functions.activeItemState.activeBacklogItem?.item}
                      sxLeft={0}
                      sxWidth={"100%"}
                      sxHeight={70}
                      onTaskClick={functions.handleTaskClick}
                      isOverlay={true}
                      from={"carryover"}
                    />
                  </Box>
                )}
              </DragOverlay>
            </Box>
          </Box>
        </Box>
      )}
    </DroppableArea>
  );
}
