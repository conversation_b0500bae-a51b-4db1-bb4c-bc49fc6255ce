// 1. React相関HOOKS
import { useRef, useEffect } from "react";

// 2. UIフレームワーク系, 外部ライブラリ
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import "dayjs/locale/ja";
import dayjs, { Dayjs } from "dayjs";
import "../App.css";

// 3. オリジナル，内部ライブラリ
import { useDailyDnD } from "../hooks/useDailyDnD";
import { useTaskGrouping } from "../hooks/useTaskGrouping";
import TaskCard from "./TaskCard";
import AnnouncementArea from "./AnnouncementArea";
import NotificationArea from "./NotificationArea";
import BackLog from "./BackLog";
import MdPullDown from "./MdPullDown";
import { CardTask, BaseTask } from "../types";
import { useDialog } from "../utils/DialogContext";
import { useTasks } from "../utils/TaskContext";
import DroppableArea from "./Droppable";
import { DraggableRowLabel } from "./DraggableRowLabel";
import CarryOver from "./CarryOver";

// 4. 画像などの静的リソース
import bgSvg from "../assets/tensen.svg";
import Dialog from "./Dialog";
import { Button } from "@mui/material";
import { useNavigate } from "react-router-dom";

const rowHeight = 80;
const cardHeight = 70;
const hourWidth = 100;
// FIXME: 4/22検証向け対応として5/2 9:05に固定
// const nowTime = new Date("2025-05-02T09:05:00.000000");
const nowTime = new Date(); //20250522毎回画面を開くときに、自動的に現在時刻を表示するようにする

const isInDateRange = (targetDate: Date, selectedDate: Dayjs) => {
  const start = selectedDate
    .subtract(1, "day")
    .hour(21)
    .minute(0)
    .second(0)
    .millisecond(0);
  const end = selectedDate
    .add(1, "day")
    .hour(9)
    .minute(0)
    .second(0)
    .millisecond(0);

  const target = dayjs(targetDate);

  return target.isAfter(start) && target.isBefore(end);
};
const calcCardPos = (item: CardTask, selectedDate: Dayjs) => {
  if (!(item.start instanceof Date) || !(item.end instanceof Date)) {
    return { sxLeft: 0, sxWidth: 100 };
  }
  const rangeStart = dayjs(selectedDate)
    .subtract(1, "day")
    .hour(21)
    .minute(0)
    .second(0)
    .millisecond(0);

  const diffMinutes = dayjs(item.start).diff(rangeStart, "minute");
  const sxLeft = Math.max(0, (diffMinutes / 60) * hourWidth);
  let sxWidth;
  const nextDay = selectedDate
    .add(1, "day")
    .hour(9)
    .minute(0)
    .second(0)
    .millisecond(0)
    .toDate();
  const prevDay = selectedDate
    .add(-1, "day")
    .hour(21)
    .minute(0)
    .second(0)
    .millisecond(0)
    .toDate();
  if (isInDateRange(item.start, selectedDate)) {
    if (isInDateRange(item.end, selectedDate)) {
      sxWidth =
        ((item.end.getTime() - item.start.getTime()) / 60000) * (100 / 60);
    } else {
      sxWidth =
        ((nextDay.getTime() - item.start.getTime()) / 60000) * (100 / 60);
    }
  } else {
    if (isInDateRange(item.end, selectedDate)) {
      sxWidth = ((item.end.getTime() - prevDay.getTime()) / 60000) * (100 / 60);
    } else {
      sxWidth = ((nextDay.getTime() - prevDay.getTime()) / 60000) * (100 / 60);
    }
  }
  return { sxLeft: sxLeft, sxWidth: sxWidth };
};

// デモ用シフト時間決め打ち
const shiftTime = [
  { name: "田中", shiftStart: 11, shiftEnd: 23 },
  { name: "平山", shiftStart: 11, shiftEnd: 21 },
  { name: "佐々木", shiftStart: 8, shiftEnd: 13 },
  { name: "森本", shiftStart: 13, shiftEnd: 20 },
  { name: "遠藤", shiftStart: 13, shiftEnd: 20 },
  { name: "岡崎", shiftStart: 21, shiftEnd: 27 },
  { name: "大澤", shiftStart: 21, shiftEnd: 27 },
  { name: "佐々木", shiftStart: 26, shiftEnd: 33 },
  { name: "林", shiftStart: 26, shiftEnd: 33 },
  // { name: "Aさん", shiftStart: 26, shiftEnd: 33 },
];

export default function DailyView() {
  const { openDialog } = useDialog();
  const navigate = useNavigate();
  const {
    refreshTasks,
    calendarTasks,
    setCalendarTasks,
    backlogTasks,
    setBacklogTasks,
    carryoverTasks,
    setCarryoverTasks,
    selectedDate,
    setSelectedDate,
  } = useTasks();
  // D&DロジックはuseDailyDnDに移動

  useEffect(() => {
    // 初期表示は仮で5/2
    refreshTasks(dayjs(selectedDate).format("YYYY-MM-DD"));
  }, [selectedDate]);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const isToday =
    dayjs(nowTime).format("YYYY-MM-DD") == selectedDate.format("YYYY-MM-DD");

  const currentTimePos =
    nowTime.getHours() * hourWidth +
    hourWidth * (nowTime.getMinutes() / 60) +
    hourWidth * 3;
  useEffect(() => {
    if (scrollContainerRef.current && isToday) {
      scrollContainerRef.current.scrollLeft = currentTimePos - 200;
    } else if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft = 1500;
    }
  }, [selectedDate, isToday]);

  const returnNowTimeSetting = (now: Date) => {
    const cardSxSetting = {
      // height: cardHeight * 3 + 25,
      height: cardHeight * assignedNames.length + 15,
      position: "absolute",
      top: (rowHeight - cardHeight) / 2 - 1,
      boxSizing: "border-box",
      border: "1px #FF82B2 solid",
      opacity: isToday ? 1 : 0,
      left: 0,
      width: 0,
    };
    cardSxSetting.left =
      now.getHours() * hourWidth +
      hourWidth * (now.getMinutes() / 60) +
      hourWidth * 3;
    return cardSxSetting;
  };

  const returnNowTimeCircleSetting = (now: Date) => {
    const cardSxSetting = {
      height: 10,
      position: "absolute",
      top: (rowHeight - cardHeight) / 2 - 10,
      borderRadius: "50%",
      opacity: isToday ? 1 : 0,
      left: 0,
      width: 10,
      backgroundColor: "#FF82B2",
    };
    cardSxSetting.left =
      now.getHours() * hourWidth +
      hourWidth * (now.getMinutes() / 60) -
      4 +
      hourWidth * 3;
    return cardSxSetting;
  };

  // useTaskGrouping呼び出し
  const {
    currentDateTasks,
    nameGroupedTask,
    assignedNames,
    setAssignedNames,
    cardPositions,
  } = useTaskGrouping({
    calendarTasks,
    selectedDate,
    shiftTime,
    calcCardPos,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 4,
      },
    })
  );

  // D&DロジックはuseDailyDnDに移動
  const handleTaskClick = (dialogData: BaseTask) => {
    try {
      openDialog("view", dialogData);
    } catch (error) {
      console.error("タスクをクリックする際にエラーが出た:", error);
    }
  };

  // useDailyDnD呼び出し
  const dnd = useDailyDnD({
    currentDateTasks,
    assignedNames,
    setAssignedNames,
    setCalendarTasks,
    backlogTasks,
    setBacklogTasks,
    carryoverTasks,
    setCarryoverTasks,
    selectedDate,
    cardPositions,
    scrollContainerRef,
    hourWidth,
  });

  const {
    activeItem,
    activeBacklogItem,
    setActiveBacklogItem,
    isDragging,
    setIsDragging,
    handleDragEnd,
    onDragStart,
  } = dnd;

  const backlogFunctions = {
    activeItemState: {
      activeBacklogItem,
      setActiveBacklogItem,
    },
    draggingState: {
      isDragging,
      setIsDragging,
    },
    handleTaskClick,
  };
  const changeSelectedDate = (newValue: dayjs.Dayjs | null) => {
    if (newValue) {
      refreshTasks(newValue.format("YYYY-MM-DD"));
      setSelectedDate(newValue);
    }
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          display: "flex",
          maxWidth: "100%",
          flexBasis: "center",
        }}
      >
        <div>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              sx={{ margin: 1 }}
              label="日付"
              value={selectedDate}
              onChange={(newValue) => changeSelectedDate(newValue)}
              format={"YYYY/MM/DD"}
            />
          </LocalizationProvider>
        </div>
        <MdPullDown></MdPullDown>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Button
            variant="contained"
            sx={{
              border: "1px solid #DE8289",
              backgroundColor: "#FFFFFF",
              color: "#DE8289",
              height: 50,
            }}
            onClick={() => navigate("/weekly")}
          >
            週次画面へ
          </Button>
        </Box>
        <NotificationArea></NotificationArea>
      </Box>
      <Box
        sx={{
          display: "flex",
          maxWidth: "100%",
          maxHeight: "calc(100vh - 160px)",
        }}
      >
        <DndContext
          sensors={sensors}
          onDragStart={onDragStart}
          onDragEnd={handleDragEnd}
        >
          <Box
            sx={{
              flexBasis: "70%",
              flexGrow: 0,
              maxWidth: "70%",
              margin: 1,
            }}
          >
            <AnnouncementArea></AnnouncementArea>

            <Stack
              direction={"row"}
              sx={{
                border: "1px gray solid",
                overflow: "visible",
              }}
            >
              <SortableContext
                items={assignedNames}
                strategy={verticalListSortingStrategy}
              >
                <Stack direction={"column"} spacing={0}>
                  <Box
                    sx={{
                      width: 150,
                      borderBottom: "1px gray solid",
                      boxSizing: "border-box",
                      height: 50,
                    }}
                  />
                  {assignedNames.map((name) => (
                    <DraggableRowLabel
                      key={name}
                      id={name}
                      name={name}
                      rowHeight={rowHeight}
                    />
                  ))}
                </Stack>
              </SortableContext>
              <Stack
                direction={"column"}
                spacing={0}
                ref={scrollContainerRef}
                sx={{
                  flexGrow: 1,
                  overflowX: "auto",
                  width: "100%",
                  position: "relative",
                }}
              >
                <Box sx={{ width: "max-content", position: "relative" }}>
                  <Stack direction={"row"} spacing={0}>
                    {Array(36)
                      .fill(0)
                      .map((_, index) => {
                        const hour = (21 + index) % 24;
                        return (
                          <Box
                            key={index}
                            sx={{
                              border: "1px rgba(25, 118, 210, 0.08) solid",
                              boxSizing: "border-box",
                              height: 50,
                              width: hourWidth,
                              flexShrink: 0,
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            {hour}:00
                          </Box>
                        );
                      })}
                  </Stack>
                </Box>
                <Box sx={{ width: "fit-content", position: "relative" }}>
                  {assignedNames.map((name) => (
                    <DroppableArea id={name} key={name}>
                      {(setNodeRef) => {
                        const shift = shiftTime.find((s) => s.name == name);
                        return (
                          <Box
                            ref={setNodeRef}
                            sx={{
                              width: hourWidth * 36,
                              border: 0,
                              boxSizing: "border-box",
                              height: rowHeight,
                              position: "relative",
                              backgroundImage: [
                                `url("${bgSvg}")`,
                                `linear-gradient(
                              to right,
                              rgb(239, 239, 239) 0 ${
                                hourWidth * (shift?.shiftStart ?? 0)
                              }px,
                              transparent ${
                                hourWidth * (shift?.shiftStart ?? 0)
                              }px 100%
                            )`,
                                `linear-gradient(
                              to right,
                              transparent 0 ${
                                hourWidth * (shift?.shiftEnd ?? 0)
                              }px,
                              rgb(239, 239, 239) ${
                                hourWidth * (shift?.shiftEnd ?? 0)
                              }px 100%
                            )`,
                                `repeating-linear-gradient(
                              to right,
                              transparent,
                              transparent ${hourWidth - 1}px,
                              rgba(0, 0, 0, 0.1) ${hourWidth - 1}px,
                              rgba(0, 0, 0, 0.1) ${hourWidth}px
                            )`,
                              ].join(", "),
                              backgroundSize: `${hourWidth}px ${rowHeight}px, 100% 100%, 100% 100%, 100% 100%`,
                              backgroundRepeat: `repeat, no-repeat, no-repeat, no-repeat`,
                              backgroundClip: "border-box",
                            }}
                          >
                            {nameGroupedTask[name]?.map((item) => {
                              if (
                                isDragging &&
                                activeItem?.item.cardId === item.cardId
                              )
                                return null;
                              const cardPos = cardPositions[item.cardId];
                              return (
                                <TaskCard
                                  key={item.cardId}
                                  item={item}
                                  sxLeft={cardPos?.sxLeft}
                                  sxWidth={cardPos?.sxWidth}
                                  sxHeight={cardHeight}
                                  onTaskClick={handleTaskClick}
                                  isOverlay={false}
                                  isOnCalendar={true}
                                  from={name}
                                />
                              );
                            })}
                          </Box>
                        );
                      }}
                    </DroppableArea>
                  ))}
                  <DragOverlay
                    dropAnimation={null}
                    style={{ pointerEvents: "auto" }}
                  >
                    {activeItem && (
                      <Box
                        sx={{
                          position: "fixed",
                          width: "100%",
                          height: cardHeight,
                          zIndex: 9999,
                        }}
                      >
                        <TaskCard
                          item={activeItem.item}
                          sxLeft={0}
                          sxWidth={
                            cardPositions[activeItem.item.cardId]?.sxWidth
                          }
                          sxHeight={cardHeight}
                          onTaskClick={handleTaskClick}
                          isOverlay={true}
                          isOnCalendar={true}
                          from={activeItem.item.assignedName ?? ""}
                        />
                      </Box>
                    )}
                  </DragOverlay>
                  <Box sx={returnNowTimeSetting(nowTime)}></Box>
                  <Box sx={returnNowTimeCircleSetting(nowTime)}></Box>
                </Box>
              </Stack>
            </Stack>
          </Box>
          <BackLog
            tasks={backlogTasks}
            hourWidth={hourWidth}
            functions={backlogFunctions}
          />
          <CarryOver
            tasks={carryoverTasks}
            hourWidth={hourWidth}
            functions={backlogFunctions}
          />
        </DndContext>
      </Box>
      <Dialog />
    </Box>
  );
}
