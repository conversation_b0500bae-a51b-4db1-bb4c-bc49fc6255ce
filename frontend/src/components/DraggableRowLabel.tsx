import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Box } from "@mui/material";

type DraggableRowLabelProps = {
  id: string;
  name: string;
  rowHeight: number;
}

export const DraggableRowLabel = ({ id, name, rowHeight }: DraggableRowLabelProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id,
    data: {
      type: "row"
    }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    cursor: isDragging ? "grabbing" : "grab",
  };

  return (
    <Box
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      sx={{
        width: "100%",
        height: rowHeight,
        // border: "1px gray solid",
        boxSizing: "border-box",
        borderRadius: "3px",
        boxShadow: "0px 2px 1px 0px rgba(152, 122, 122, 0.5), inset 0px -3px 6px -2px rgba(0, 0, 0, 0.3)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#F8FCFF",
        ...style,
      }}
    >
      {name}
    </Box>
  );
};
