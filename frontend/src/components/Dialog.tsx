import React, { useState, useEffect, useMemo, SetStateAction } from "react";
import "../App.css";
import {
  Box,
  TextField,
  Button,
  Typography,
  ToggleButtonGroup,
  ToggleButton,
  IconButton,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from '@mui/icons-material/Add';
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Link } from "@mui/material";
import "dayjs/locale/ja";
import dayjs, { Dayjs } from "dayjs";
import { useDialog } from "../utils/DialogContext";
import { DialogTask, isBaseTask } from "../types";
import { useTasks } from "../utils/TaskContext";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import { DateTimeValidationError } from "@mui/x-date-pickers/models";
import { dataAlignerOnEdit, dataAlignerOnHistoryEdit } from "../utils/taskDataAlignment";

const Dialog: React.FC = () => {
  const { isOpen, mode, task, closeDialog, setMode } = useDialog();
  const { tasks, updateTask, createTask, removeTask } = useTasks();
  const [dialogTask, setDialogTask] = useState<DialogTask>({
    taskId: "",
    taskName: "",
    status: "未着手",
    category: "",
    location: "",
    scheduledTime: 60,
    details: "",
    source: "",
    assignedName: "",
    start: null,
    end: null,
    mode: mode,
    dueDate: null,
    history: [],
  });

  const [dateTimeError, setDateTimeError] =
    useState<SetStateAction<DateTimeValidationError>>(null);

  const isSubmitDisabled = useMemo(() => {
    if (dateTimeError) return true;
    else return false;
  }, [dateTimeError]);

  const blankHistoryObject = {
    name: "",
    start: undefined,
    end: undefined,
    status: "完了"
  }
  // ダイアログの表示を切り替える時にエラーをリセットする
  useEffect(() => {
    if (isOpen === true) {
      setDateTimeError(null);
    }
  }, [isOpen]);

  const convertToDayjs = (date: Date | null): Dayjs | null => {
    if (!date) return null;
    try {
      return dayjs(date);
    } catch (e) {
      console.error("日付転換エラー:", e);
      return null;
    }
  };

  useEffect(() => {
    if (mode === "new") {
      //  新規作成する際に利用する
      setDialogTask({
        taskId: crypto.randomUUID(),
        taskName: "",
        status: "未着手",
        category: "",
        location: "",
        scheduledTime: 60,
        details: "",
        source: "",
        assignedName: "",
        start: null,
        end: null,
        mode: mode,
        history: [],
      });
    } else if (mode === "historyEdit") {
      // 履歴編集
      const history = (() => {
        if (task?.history?.length === 0 || !task?.history) return [blankHistoryObject]
        else return task.history
      })()
      setDialogTask({
        ...task,
        status: task?.status ?? "未着手",
        taskId: task?.taskId ?? crypto.randomUUID(),
        scheduledTime: task?.scheduledTime ?? 60,
        taskName: task?.taskName ?? "",
        details: task?.details ?? "",
        category: task?.category ?? "",
        location: task?.location ?? "",
        mode: mode,
        assignedName: task?.assignedName ?? "",
        start: task?.start ?? null,
        end: task?.end ?? null,
        source: task?.source ?? "",
        dueDate: task?.dueDate ?? null,
        history: history,
      });
    } else {
      // 既存タスクを見る、編集する場合
      setDialogTask({
        ...task,
        status: task?.status ?? "未着手",
        taskId: task?.taskId ?? crypto.randomUUID(),
        scheduledTime: task?.scheduledTime ?? 60,
        taskName: task?.taskName ?? "",
        details: task?.details ?? "",
        category: task?.category ?? "",
        location: task?.location ?? "",
        mode: mode,
        assignedName: task?.assignedName ?? "",
        start: task?.start ?? null,
        end: task?.end ?? null,
        source: task?.source ?? "",
        dueDate: task?.dueDate ?? null,
        history: task?.history ?? [],
      });
    }
  }, [mode, task]);
  const isViewMode = mode === "view";

  const viewModeProps = isViewMode
    ? {
      slotProps: {
        input: {
          readOnly: true,
        },
      },
    }
    : {
      slotProps: {
        input: {
          readOnly: false,
        },
      },
    };

  const handleSave = async () => {
    try {
      //dialogTaskには、APIに渡すべきではないmodeが含まれているため、ここで分離する
      const { mode: mode, ...taskForBackend } = dialogTask;
      if (!isBaseTask(taskForBackend)) return

      if (mode === "new") {

        await createTask(taskForBackend);
      } else if (mode === "historyEdit") {
        const formattedTask = dataAlignerOnHistoryEdit(taskForBackend)
        await updateTask(formattedTask);

      } else {
        // ステータスが完了の場合はendを現在時刻として送信
        if (dialogTask.status === "完了") {
          taskForBackend.end = new Date();
        }
        const formattedTask = dataAlignerOnEdit(taskForBackend, tasks)
        await updateTask(formattedTask);
      }
      closeDialog();
    } catch (e: unknown) {
      if (e instanceof Error) {
        alert(e.message || "タスク保存に失敗しました");
      } else {
        alert("タスク保存に失敗しました");
      }
    }
  };

  const handleDelete = () => {
    const { mode: mode, ...taskForBackend } = dialogTask;
    if (!isBaseTask(taskForBackend)) return
    removeTask(taskForBackend);
    closeDialog();
  };
  const calcWorkedTIme = (start: Date | undefined, end: Date | undefined) => {
    if (start && end) {
      const worked_time = Math.round(
        Math.abs(end.getTime() - start.getTime()) / (1000 * 60)
      );
      return worked_time;
    } else {
      return 0;
    }
  };
  // workedTime = historyの時間合計
  const getWorkedTime = (dialogTask: DialogTask) => {
    if (dialogTask.history) {
      const sum_worked_time = dialogTask.history.reduce(
        (sum, task) => sum + calcWorkedTIme(task.start, task.end),
        0
      );
      return sum_worked_time;
    }
    return 0;
  };
  const getRemainingTime = (dialogTask: DialogTask): string => {
    const worked_time = getWorkedTime(dialogTask);
    if (dialogTask?.scheduledTime != null && worked_time != null) {
      return `${Math.floor(dialogTask.scheduledTime - worked_time)} 分`;
    }
    return `${dialogTask.scheduledTime} 分`;
  };

  const deleteHistory = (index: number) => {
    const tempTask = { ...dialogTask }
    if (tempTask.history) {
      tempTask.history.splice(index, 1)
      setDialogTask(tempTask)
    }
  }
  const addHistory = () => {
    const tempTask = { ...dialogTask }
    if (!tempTask.history) {
      tempTask.history = []
    }
    tempTask.history.push(blankHistoryObject)
    setDialogTask(tempTask)
  }

  if (!isOpen) {
    return null;
  } else {

    return (
      <div className="dialog-overlay">
        <div className="dialog-content">
          <Box
            sx={{
              display: "flex",
              justifyContent: "right",
              alignItems: "center",
              mb: 2,
              position: "sticky",
              backgroundColor: "white",
              zIndex: 1,
            }}
          >
            <IconButton onClick={closeDialog}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Stack
            spacing={3}
            sx={{
              width: "100%",
              "& .MuiToggleButtonGroup-root": { width: "100%" },
            }}
          >
            <Box>
              <TextField
                rows={4}
                label="タスクのタイトル"
                fullWidth
                value={dialogTask?.taskName}
                onChange={(e) =>
                  setDialogTask({
                    ...dialogTask,
                    taskId: task?.taskId ?? "",
                    taskName: e.target.value,
                  })
                }
                variant="outlined"
                {...viewModeProps}
              />
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                タスク詳細
              </Typography>
              <TextField
                multiline
                rows={4}
                fullWidth
                value={dialogTask?.details}
                onChange={(e) =>
                  setDialogTask({ ...dialogTask, details: e.target.value })
                }
                variant="outlined"
                {...viewModeProps}
              />
            </Box>

            {isViewMode && (
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                  発信元
                </Typography>
                {dialogTask.source ? (
                  <Link
                    href="about:blank"
                    target="_blank"
                    rel="noopener noreferrer"
                    color="textPrimary"
                    sx={{ fontSize: "1rem", textOverflow: "ellipsis" }}
                  >
                    {dialogTask.source}
                  </Link>
                ) : (
                  <Typography color="textPrimary" sx={{ fontSize: "1rem" }}>
                    未確定
                  </Typography>
                )}
              </Box>
            )}

            {isViewMode && (
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                  期日
                </Typography>
                <Typography color="textPrimary" sx={{ fontSize: "1rem" }}>
                  {dialogTask.dueDate
                    ? convertToDayjs(dialogTask.dueDate)?.format("YYYY/MM/DD")
                    : null}
                </Typography>
              </Box>
            )}

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                カテゴリ
              </Typography>
              <TextField
                fullWidth
                value={dialogTask?.category}
                onChange={(e) =>
                  setDialogTask({ ...dialogTask, category: e.target.value })
                }
                variant="outlined"
                {...viewModeProps}
                sx={{ width: "200px" }}
              />
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                場所
              </Typography>
              <TextField
                fullWidth
                value={dialogTask?.location}
                onChange={(e) =>
                  setDialogTask({ ...dialogTask, location: e.target.value })
                }
                variant="outlined"
                {...viewModeProps}
                sx={{ width: "200px" }}
              />
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                ステータス
              </Typography>
              <ToggleButtonGroup
                value={dialogTask?.status}
                exclusive
                onChange={(_, newStatus) => {
                  if (mode === "edit" && dialogTask.status !== "未着手") {
                    setDialogTask({
                      ...dialogTask,
                      status: newStatus,
                      // endは保存時にのみセットする
                    });
                  }
                }}
                aria-label="task status"
                disabled={mode === "edit" && dialogTask.status === "未着手"}
              >
                <ToggleButton
                  value="未着手"
                  disabled={mode === "edit" && dialogTask.status !== "未着手"}
                >
                  未着手
                </ToggleButton>
                <ToggleButton value="進行中">進行中</ToggleButton>
                <ToggleButton value="完了">完了</ToggleButton>
                <ToggleButton value="中断">中断</ToggleButton>
              </ToggleButtonGroup>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                作業予定時間（分）
              </Typography>
              <TextField
                type="number"
                value={dialogTask?.scheduledTime}
                onChange={(e) =>
                  setDialogTask({
                    ...dialogTask,
                    scheduledTime: Number(e.target.value),
                  })
                } //error handling needed
                variant="outlined"
                {...viewModeProps}
                sx={{ width: "100px" }}
              />
              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                作業実績時間（分）
              </Typography>
              <TextField
                type="number"
                value={getWorkedTime(dialogTask)}
                variant="outlined"
                {...viewModeProps}
                sx={{ width: "100px" }}
              />

              <Typography variant="subtitle1" sx={{ minWidth: "100px" }}>
                残り時間（分）
              </Typography>
              {getRemainingTime(dialogTask)}
            </Box>

            <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2 }}>
              <Typography
                variant="subtitle1"
                sx={{ minWidth: "100px", mt: "15px" }}
              >
                実施予定
              </Typography>
              <LocalizationProvider
                dateAdapter={AdapterDayjs}
                adapterLocale="ja"
              >
                <Stack spacing={2}>
                  <Box
                    key={dialogTask.taskId}
                    sx={{ display: "flex", alignItems: "center", gap: 2 }}
                  >
                    <TextField
                      value={dialogTask.assignedName || ""}
                      onChange={(e) => {
                        setDialogTask((prev) => ({
                          ...prev,
                          assignedName: e.target.value,
                        }));
                      }}
                      variant="outlined"
                      sx={{ width: "200px" }}
                      {...viewModeProps}
                    />
                    <DateTimePicker
                      value={
                        dialogTask.start
                          ? convertToDayjs(dialogTask.start)
                          : null
                      }
                      onChange={(newValue) => {
                        setDialogTask({
                          ...dialogTask,
                          start: newValue ? newValue.toDate() : null,
                        });
                      }}
                      disabled={isViewMode}
                      maxDateTime={
                        convertToDayjs(dialogTask.end ?? null) ?? undefined
                      }
                      onError={(newError) => setDateTimeError(newError)}
                    />
                    <Typography>~</Typography>

                    <Box>
                      <DateTimePicker
                        value={
                          dialogTask.end ? convertToDayjs(dialogTask.end) : null
                        }
                        onChange={(newValue) => {
                          setDialogTask({
                            ...dialogTask,
                            end: newValue ? newValue.toDate() : null,
                          });
                        }}
                        disabled={isViewMode || dialogTask.status === "完了"}
                        minDateTime={
                          convertToDayjs(dialogTask.start ?? null) ?? undefined
                        }
                      />
                    </Box>
                  </Box>
                </Stack>
              </LocalizationProvider>
            </Box>
            <Box>
              {dialogTask.status === "完了" && dialogTask.mode === "edit" ? (
                <Typography
                  color="textSecondary"
                  sx={{ fontSize: "0.8rem", textAlign: "right" }}
                >
                  ステータスが完了のため、終了時刻は編集できません
                </Typography>
              ) : null}
            </Box>

            {(mode === "historyEdit" || (dialogTask?.history?.length !== 0 &&
              dialogTask.history !== undefined)) && (
                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 2 }}>
                  <Box
                    sx={{ minWidth: 100, width: 170, display: "flex", alignItems: "center" }}
                  >
                    作業履歴
                    <AccessTimeIcon />
                  </Box>
                  <Box sx={{ width: "100%", paddingRight: "5em" }}>
                    {dialogTask?.history?.map((his, index) =>

                      (mode === "historyEdit") ?
                        (
                          <LocalizationProvider
                            dateAdapter={AdapterDayjs}
                            adapterLocale="ja"
                            key={dialogTask.taskId + "-history-" + index}
                          >
                            <Stack spacing={2} sx={{ mb: 1 }}>
                              <Box
                                sx={{ display: "flex", alignItems: "center", gap: 1 }}
                              >
                                <TextField
                                  value={his.name || ""}
                                  onChange={(event) => {
                                    setDialogTask((prev) => {
                                      const updatedData = { ...prev }
                                      updatedData.history![index] = {
                                        ...prev.history![index],
                                        name: event.target.value
                                      }
                                      return updatedData
                                    });
                                  }}
                                  variant="outlined"
                                  sx={{ width: "100px" }}
                                  {...viewModeProps}
                                />
                                <DateTimePicker
                                  value={
                                    his.start
                                      ? convertToDayjs(his.start)
                                      : null
                                  }
                                  onChange={(newValue) => {
                                    const updatedData = { ...dialogTask }
                                    updatedData.history![index] = {
                                      ...dialogTask.history![index],
                                      start: newValue ? newValue.toDate() : undefined,
                                    }
                                    setDialogTask(updatedData);
                                  }}
                                  disabled={isViewMode}
                                  maxDateTime={convertToDayjs(his.end ?? new Date()) ?? undefined}
                                  onError={(newError) => setDateTimeError(newError)}
                                  sx={{ width: "200px" }}
                                />
                                <Typography>~</Typography>
                                <DateTimePicker
                                  value={
                                    his.end ? convertToDayjs(his.end) : null
                                  }
                                  onChange={(newValue) => {
                                    const updatedData = dialogTask
                                    updatedData.history![index] = {
                                      ...dialogTask.history![index],
                                      end: newValue ? newValue.toDate() : undefined,
                                    }
                                    setDialogTask(updatedData);
                                  }}
                                  disabled={isViewMode}
                                  minDateTime={convertToDayjs(his.start ?? null) ?? undefined}
                                  maxDateTime={convertToDayjs(new Date()) ?? undefined}
                                  sx={{ width: "200px" }}
                                />
                                <FormControl>
                                  <Select
                                    labelId="demo-simple-select-label"
                                    id="demo-simple-select"
                                    value={his.status}
                                    label="Age"
                                    onChange={(event) => {
                                      const updatedData = { ...dialogTask }
                                      updatedData.history![index] = {
                                        ...dialogTask.history![index],
                                        status: event.target.value,
                                      }
                                      setDialogTask(updatedData);
                                    }}
                                  >
                                    <MenuItem value={"完了"}>完了</MenuItem>
                                    <MenuItem value={"中断"}>中断</MenuItem>
                                  </Select>
                                </FormControl>
                                <IconButton aria-label="delete" onClick={() => deleteHistory(index)}>
                                  <DeleteIcon />
                                </IconButton>
                              </Box>
                            </Stack>
                          </LocalizationProvider>

                        ) :
                        (
                          <Box
                            key={dialogTask.taskId + index}
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: 1,
                            }}
                          >
                            <Box sx={{ flex: 1 }}>
                              <Typography>{his.name}</Typography>
                            </Box>
                            <Box
                              sx={{
                                flex: 1,
                                display: "flex",
                                justifyContent: "center",
                              }}
                            >
                              <Typography sx={{ whiteSpace: "nowrap" }}>
                                {his.start?.toLocaleString()} 〜{" "}
                                {his.end?.toLocaleString()}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                flex: 1,
                                display: "flex",
                                justifyContent: "center",
                              }}
                            >
                              <Typography
                                sx={{ display: "flex", alignItems: "center" }}
                              >
                                {his.status}
                              </Typography>
                            </Box>
                          </Box>
                        )
                    )}
                    {(mode === "historyEdit") &&
                      <IconButton aria-label="add" onClick={() => addHistory()}>
                        <AddIcon />

                      </IconButton>}
                  </Box>
                </Box>
              )}

            {isViewMode && (
              <Box sx={{ display: "flex", justifyContent: "left", mt: 2, gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setMode("edit")}
                >
                  編集
                </Button>

                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setMode("historyEdit")}
                >
                  履歴編集
                </Button>
              </Box>
            )}

            {!isViewMode && (
              <Box
                sx={{ display: "flex", justifyContent: "left", mt: 2, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="success"
                  disabled={isSubmitDisabled}
                  onClick={handleSave}
                >
                  保存
                </Button>
                <Button
                  variant="contained"
                  color="error"
                  onClick={handleDelete}
                >
                  削除
                </Button>
              </Box>
            )}
          </Stack>
        </div>
      </div>
    );
  }
};

export default Dialog;
