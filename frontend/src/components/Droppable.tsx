import { useDroppable } from '@dnd-kit/core';

type DroppableAreaProps = {
  id: string;
  children: (setNodeRef: (el: HTMLElement | null) => void, isOver: boolean) => React.ReactNode;
}

const DroppableArea = ({ id, children }: DroppableAreaProps) => {
  const { setNodeRef, isOver } = useDroppable({
    id
  });

  return (
    <>
      {children(setNodeRef, isOver)}
    </>
  );
};

export default DroppableArea;
