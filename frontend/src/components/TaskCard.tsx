import { useEffect } from "react";
import { Box, LinearProgress, Typography } from "@mui/material";
import GroupIcon from "@mui/icons-material/Group";
import GraphicEqIcon from "@mui/icons-material/GraphicEq";
import { useDraggable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { CardTask, BaseTask } from "../types/task";
import { useResizeHandlers } from "../utils/useResizeHandlers";

type TaskProps = {
  item: CardTask;
  sxLeft: number;
  sxWidth: number | string;
  sxHeight: number;
  onTaskClick: (dialogData: BaseTask) => void;
  isOverlay: boolean;
  isOnCalendar?: boolean;
  from: string;
};

export default function TaskCard({
  item,
  sxLeft,
  sxWidth,
  sxHeight,
  onTaskClick,
  isOnCalendar,
  isOverlay,
  from,
}: TaskProps) {
  // 固定値
  const cardHeight = 70;
  const rowHeight = 80;
  const hourWidth = 100; // 1時間に相当するpx幅

  // 稼働済み時間（分）
  const workedTime = (() => {
    return item.baseTask.history?.reduce(
      (acc, record) =>
        acc + (record.end.getTime() - record.start.getTime()) / 1000 / 60,
      0
    );
  })();

  const {
    setLocalWidth,
    setLocalLeft,
    isResizing,
    startX,
    originalWidth,
    originalLeft,
    localLeft,
    localWidth,
    updateTaskTime,
    resizeOrigin,
    handleResizeStart,
    handleResizeMove,
    handleResizeEnd,
  } = useResizeHandlers({ item, sxWidth, sxLeft, hourWidth });

  // propが変わったとき、ローカルstateも同期させる（リサイズ終了後や表示切替時）
  useEffect(() => {
    setLocalWidth(sxWidth);
    setLocalLeft(typeof sxLeft === "number" ? sxLeft : 0);
  }, [sxWidth, sxLeft]);

  useEffect(() => {
    if (isResizing) {
      window.addEventListener("mousemove", handleResizeMove);
      window.addEventListener("mouseup", handleResizeEnd);
    }

    // クリーンアップ
    return () => {
      window.removeEventListener("mousemove", handleResizeMove);
      window.removeEventListener("mouseup", handleResizeEnd);
    };
    // 依存配列にlocalXXX等を含める
  }, [
    isResizing,
    startX,
    originalWidth,
    originalLeft,
    localLeft,
    localWidth,
    item.cardId,
    updateTaskTime,
    resizeOrigin,
  ]);

  // DnD-kitのDraggableフック
  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: item.cardId,
      data: { type: "task", from, item },
      disabled: item.isHistoryData,
    });

  // 日付フォーマット（YYYY/MM/DD）
  const formatDate = (date: Date | null | undefined) => {
    if (!date) return "";
    return `${date.getFullYear()}/${(date.getMonth() + 1)
      .toString()
      .padStart(2, "0")}/${date.getDate().toString().padStart(2, "0")}`;
  };

  // 詳細表示用クリック関数
  const handleClick = () => {
    try {
      onTaskClick({
        ...item.baseTask,
      });
    } catch (error) {
      console.error("タスクカードをクリックした際にエラーが発生:", error);
    }
  };

  // カードクリック時の処理（リサイズ中やD&D中は無効）
  const handleMouseUp = () => {
    if (!isDragging && !isResizing) {
      handleClick();
    }
  };

  // 関連アイコン
  const iconMap = {
    Teams: GroupIcon,
    インカム: GraphicEqIcon,
  } as const;

  // source文字列→Icon
  const returnSourceIcon = (source: string) => {
    const IconComponent = iconMap[source as keyof typeof iconMap];
    return IconComponent ? <IconComponent /> : null;
  };

  // ステータス→色
  const sxBackGroundColor = (status: string, isHistoryData: boolean) => {
    if (isHistoryData) return "#D5CDCD";
    if (status === "未着手") return "#FFFFFF";
    if (status === "進行中") return "#C2DCF6";
    if (status === "完了") return "#D5CDCD";
    if (status === "中断") return "#E8A7AC";
    return "#ffffff";
  };

  // 締切超過判定
  const isOverDueDate = (due: Date | null | undefined) => {
    const today = new Date();
    if (due == null || due == undefined) return false;
    if (today > due) return true;
    return false;
  };

  // D&D用の追加スタイル
  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const cardTextStyle = {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    flex: 1,
    minHeight: 0,
    display: "flex",
    alignItems: "center",
  };

  return (
    <Box
      // {...listeners} // D&Dとの領域が被らない場合は有効化
      {...attributes}
      ref={setNodeRef}
      style={style}
      key={item.cardId}
      onMouseUp={handleMouseUp}
      sx={{
        height: sxHeight,
        position: isOverlay ? "fixed" : "absolute",
        top: (rowHeight - cardHeight) / 2 - 1,
        boxSizing: "border-box",
        border: isOverDueDate(item.dueDate)
          ? "2px red solid"
          : "2px gray solid",
        boxShadow: "0px 5px 10px 0px rgba(0, 0, 0, 0.35)",
        borderRadius: "4px",
        left: localLeft, // ローカルステートを参照
        width: localWidth, // string | number 両対応
        backgroundColor: sxBackGroundColor(item.status, item.isHistoryData),
        padding: 1,
        cursor: isDragging ? "grabbing" : isResizing ? "col-resize" : "grab",
        zIndex: 100,
        display: "flex",
        userSelect: isResizing ? "none" : "auto", // リサイズ中文字選択禁止
        pointerEvents: isDragging ? "none" : "auto",
        transition: isResizing ? "none" : "left 0.2s, width 0.2s", // 視覚効果
        WebkitUserSelect: "none",
      }}
    >
      {(() => {
        if (
          !item.isHistoryData &&
          item.assignedName &&
          item.start &&
          item.end
        ) {
          return (
            <>
              {/* 左側のリサイズハンドル */}
              <Box
                sx={{
                  position: "absolute",
                  left: 0,
                  top: 0,
                  width: 5,
                  height: "100%",
                  cursor: "col-resize",
                  zIndex: 101,
                  background: isResizing === "left" ? "#e3f2fd" : "transparent", // ドラッグ中色
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  handleResizeStart(e, "left");
                }}
              />

              {/* 右側のリサイズハンドル */}
              <Box
                sx={{
                  position: "absolute",
                  right: 0,
                  top: 0,
                  width: 5,
                  height: "100%",
                  cursor: "col-resize",
                  zIndex: 101,
                  background:
                    isResizing === "right" ? "#e3f2fd" : "transparent",
                }}
                onMouseDown={(e) => {
                  e.stopPropagation();
                  handleResizeStart(e, "right");
                }}
              />
            </>
          );
        } else {
          return <></>;
        }
      })()}

      {/* カード本体（内容） */}
      <Box
        sx={{
          width: "85%",
          height: "100%",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        <Box
          {...listeners}
          sx={{
            width: "90%",
            height: "100%",
            cursor: "grab",
            display: "flex",
            flexDirection: "column",
            gap: "2px",
          }}
        >
          <Box
            sx={{
              ...cardTextStyle,
              fontSize: 14,
            }}
          >
            {item.taskName}
          </Box>
          {!isOnCalendar && (
            <Box
              sx={{
                ...cardTextStyle,
                fontSize: 12,
              }}
            >
              {item.assignedName}
            </Box>
          )}
          <Box
            sx={{
              ...cardTextStyle,
              fontSize: 12,
              color: isOverDueDate(item.dueDate) ? "red" : "",
              fontWeight: isOverDueDate(item.dueDate) ? "bold" : "",
            }}
          >
            {formatDate(item.dueDate)}
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          position: "absolute",
          right: 8,
          top: "40%",
          transform: "translateY(-50%)",
          display: "flex",
          alignItems: "center",
        }}
      >
        {returnSourceIcon(item.source ?? "")}
      </Box>
      {/* 中断時の進捗バーと残り分数 */}
      {item.status === "中断" &&
        typeof item.scheduledTime === "number" &&
        typeof workedTime === "number" && (
          <Box
            sx={{
              position: "absolute",
              right: 8,
              bottom: 8,
              width: "50%",
              zIndex: 102,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                display: "block",
                textAlign: "right",
                fontSize: 10,
                fontWeight: 400,
              }}
            >
              残り{Math.max(0, Math.floor(item.scheduledTime - workedTime))}分
            </Typography>
            <LinearProgress
              variant="determinate"
              value={
                item.scheduledTime > 0
                  ? Math.min(
                    100,
                    Math.round((workedTime / item.scheduledTime) * 100)
                  )
                  : 0
              }
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}
    </Box>
  );
}
