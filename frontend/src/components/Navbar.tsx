import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
// import MenuIcon from "@mui/icons-material/Menu";
import AccountCircleOutlinedIcon from "@mui/icons-material/AccountCircleOutlined";
// import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import MenuItem from "@mui/material/MenuItem";
import Menu from "@mui/material/Menu";
// import LogoutIcon from "@mui/icons-material/Logout";
import { useNavigate } from "react-router-dom";
import { UserContext } from "../utils/AccountContext";
import { useContext } from "react";
// import { Tooltip } from "@mui/material";
import { BASE_URL } from "../utils/api";

interface NavbarProps {
  // onMenuClick: () => void;
  title: string;
  showBackButton?: boolean;
}

export default function Navbar({
  // onMenuClick,
  title,
}: // showBackButton = false,
NavbarProps) {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const navigate = useNavigate();
  const { setUsername } = useContext(UserContext);
  const handleLogout = async () => {
    try {
      const response = await fetch(`${BASE_URL}/auth/logout`, {
        method: "POST",
        credentials: "include",
      });

      if (!response.ok) {
        console.warn("Server logout failed, cleaning up locally");
      }

      localStorage.removeItem("username");
      sessionStorage.clear();
      setUsername("");

      navigate("/login", { replace: true });
    } catch (error) {
      console.error("Logout error:", error);
      localStorage.removeItem("username");
      sessionStorage.clear();
      setUsername("");
      navigate("/login", { replace: true });
    }
  };
  // const handleBack = () => {
  //   navigate("/");
  // };

  const { username } = React.useContext(UserContext);

  const AllUsername: Record<string, string> = {
    "Tokyo#Aobadai#1MD": "五反田東口４MD",
    // "Tokyo#Aobadai#1MD": "東京青葉台１MD",
    "Tokyo#Aobadai#2MD": "東京青葉台２MD",
    "Tokyo#Aobadai#4MD": "東京青葉台４MD",
    "Tokyo#Aobadai#6MD": "東京青葉台６MD",
    "PPIH-IT-Support": "PPIH-事務局",
    "Test-ISE": "ISE Internal",
    "Test-IBMC": "IBMC",
    "Tokyo-Nakameguro-6MD": "東京中目黒６MD",
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppbarのzIndexをdrawerの上に置く（drawerのzIndexは1200） */}
      <AppBar
        position="fixed"
        sx={{ zIndex: 1201, backgroundColor: "#519BE5", height: "70px" }}
      >
        <Toolbar>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              position: "relative",
              width: "100%",
            }}
          >
            <Box sx={{ display: "flex", justifyContent: "flex-start" }}>
              {/* <IconButton
                size="large"
                edge="start"
                color="inherit"
                aria-label="menu"
                sx={{ mr: 2 }}
                // onClick={onMenuClick}
              >
                <MenuIcon />
              </IconButton> */}
              {/* TopPage */}
              {/* {showBackButton && (
                <IconButton
                  size="large"
                  edge="start"
                  color="inherit"
                  aria-label="back"
                  onClick={handleBack}
                  sx={{ mr: 2 }}
                >
                  <Tooltip title="トップページへ">
                    <ArrowBackIcon />
                  </Tooltip>
                </IconButton>
              )} */}
            </Box>
            <Box
              sx={{
                position: "absolute",
                left: "50%",
                transform: "translateX(-50%)",
                textAlign: "center",
              }}
            >
              <Typography variant="h4">{title}</Typography>
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <IconButton
                size="large"
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                onClick={handleMenu}
                color="inherit"
                sx={{
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                <AccountCircleOutlinedIcon />
                <Typography variant="caption">
                  {AllUsername[username]}
                </Typography>
              </IconButton>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                keepMounted
                transformOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
              >
                {/* <MenuItem onClick={handleClose}>プロフィール</MenuItem> */}
                <MenuItem onClick={handleLogout}>ログアウト</MenuItem>
              </Menu>
            </Box>
          </Box>
        </Toolbar>
      </AppBar>
    </Box>
  );
}
