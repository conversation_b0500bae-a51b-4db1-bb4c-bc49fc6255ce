// 1. React相関HOOKS
import { useRef, useEffect, useState } from "react";

// 2. UIフレームワーク系, 外部ライブラリ
import Box from "@mui/material/Box";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { PickersDay } from "@mui/x-date-pickers/PickersDay";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import "dayjs/locale/ja";
import dayjs, { Dayjs } from "dayjs";
import { PickersDayProps } from "@mui/x-date-pickers/PickersDay";
import weekday from "dayjs/plugin/weekday";
import isSameOrAfter from "dayjs/plugin/weekday";
import isSameOrBefore from "dayjs/plugin/weekday";
import "../App.css";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";

dayjs.extend(weekday);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

declare module "dayjs" {
  interface Dayjs {
    isSameOrAfter(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
    isSameOrBefore(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
  }
}

type dayProps = PickersDayProps<Dayjs>;

// 3. オリジナル，内部ライブラリ
import MdPullDown from "./MdPullDown";
import { useTasks } from "../utils/TaskContext";

import Weekly1 from "./Weekly1";
import Weekly2 from "./Weekly2";

const hourWidth = 12;
// FIXME: 4/22検証向け対応として5/2 9:05に固定
// const nowTime = new Date("2025-05-02T09:05:00.000000");
const nowTime = new Date(); //20250522毎回画面を開くときに、自動的に現在時刻を表示するようにする

export default function WeeklyView() {
  const {
    refreshWeeklyTasks,
    selectedDate,
    setSelectedDate,
    weeklyTabValue,
    setWeeklyTabValue,
  } = useTasks();
  // D&DロジックはuseDailyDnDに移動

  const getWeekStart = (date: Dayjs) => date.weekday(1); // 月曜日
  const getWeekEnd = (date: Dayjs) => date.weekday(7);

  useEffect(() => {
    // 初期表示は仮で5/2
    refreshWeeklyTasks(
      getWeekStart(selectedDate).format("YYYY-MM-DD"),
      getWeekEnd(selectedDate).format("YYYY-MM-DD"),
    );
  }, [selectedDate]);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const isToday =
    dayjs(nowTime).format("YYYY-MM-DD") == selectedDate.format("YYYY-MM-DD");

  const currentTimePos =
    nowTime.getHours() * hourWidth +
    hourWidth * (nowTime.getMinutes() / 60) +
    hourWidth * 3;
  useEffect(() => {
    if (scrollContainerRef.current && isToday) {
      scrollContainerRef.current.scrollLeft = currentTimePos - 200;
    } else if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft = 1500;
    }
  }, [selectedDate, isToday]);

  const changeSelectedDate = (newValue: dayjs.Dayjs | null) => {
    if (newValue) {
      refreshWeeklyTasks(
        getWeekStart(newValue).format("YYYY-MM-DD"),
        getWeekEnd(newValue).format("YYYY-MM-DD"),
      );
      setSelectedDate(newValue);
    }
  };

  const renderWeekDate = (date: Dayjs) => {
    const start = getWeekStart(date);
    const end = getWeekEnd(date);
    return `${start.format("YYYY-MM-DD")} ~ ${end.format("YYYY-MM-DD")}`;
  };

  const renderWeekHighlight = (
    date: Dayjs,
    selectedDate: Dayjs,
    pickersDayProps: dayProps,
  ) => {
    const start = getWeekStart(selectedDate);
    const end = getWeekEnd(selectedDate).add(1, "day");

    const isInWeek = date.isAfter(start, "day") && date.isBefore(end, "day");

    return (
      <PickersDay
        {...pickersDayProps}
        sx={{
          backgroundColor: isInWeek ? "#1976d2" : undefined,
          color: isInWeek ? "#fff" : undefined,
          borderRadius: 0,
        }}
      />
    );
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          display: "flex",
          maxWidth: "100%",
          flexBasis: "center",
        }}
      >
        <div>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              sx={{
                margin: 1,
                width: 250,
              }}
              label="週"
              value={selectedDate}
              onChange={(newValue) => changeSelectedDate(newValue)}
              format={renderWeekDate(selectedDate)}
              slots={{
                day: (dayProps) =>
                  renderWeekHighlight(dayProps.day, selectedDate, dayProps),
              }}
            />
          </LocalizationProvider>
        </div>
        <MdPullDown></MdPullDown>
      </Box>
      <Tabs
        value={weeklyTabValue}
        onChange={(_, newValue) => setWeeklyTabValue(newValue)}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="週次画面-1" />
        <Tab label="週次画面-2" />
      </Tabs>
      {weeklyTabValue === 0 && <Weekly1 />}
      {weeklyTabValue === 1 && <Weekly2 />}
    </Box>
  );
}
