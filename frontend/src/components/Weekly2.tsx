import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import dayjs, { Dayjs } from "dayjs";
import weekday from "dayjs/plugin/weekday";
import isSameOrAfter from "dayjs/plugin/weekday";
import isSameOrBefore from "dayjs/plugin/weekday";
import { useNavigate } from "react-router-dom";
import "../App.css";

dayjs.extend(weekday);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

declare module "dayjs" {
  interface Dayjs {
    isSameOrAfter(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
    isSameOrBefore(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
  }
}
import bgSvg from "../assets/tensen.svg";

import { Button, Typography } from "@mui/material";
import { useTasks } from "../utils/TaskContext";
import { useTaskGrouping } from "../hooks/useTaskGrouping";
import { CardTask } from "../types";

const rowHeight = 50;
const hourWidth = 12;
const dayWidth = hourWidth * 24;

const isInDateRange = (targetDate: Date, selectedDate: Dayjs) => {
  const start = selectedDate.weekday(1);
  const end = selectedDate.weekday(7);

  const target = dayjs(targetDate);

  return target.isAfter(start) && target.isBefore(end);
};
const calcCardPos = (item: CardTask, selectedDate: Dayjs) => {
  if (!(item.start instanceof Date) || !(item.end instanceof Date)) {
    return { sxLeft: 0, sxWidth: 100 };
  }
  const rangeStart = dayjs(selectedDate).weekday(1);

  const diffMinutes = dayjs(item.start).diff(rangeStart, "minute");
  const sxLeft = Math.max(0, (diffMinutes / 60) * hourWidth);
  let sxWidth;
  const week_end = selectedDate.weekday(7).add(1, "day").toDate();
  const week_start = selectedDate.weekday(1).toDate();
  if (isInDateRange(item.start, selectedDate)) {
    if (isInDateRange(item.end, selectedDate)) {
      sxWidth =
        ((item.end.getTime() - item.start.getTime()) / 60000) *
        (hourWidth / 60);
    } else {
      sxWidth =
        ((week_end.getTime() - item.start.getTime()) / 60000) *
        (hourWidth / 60);
    }
  } else {
    if (isInDateRange(item.end, selectedDate)) {
      sxWidth =
        ((item.end.getTime() - week_start.getTime()) / 60000) *
        (hourWidth / 60);
    } else {
      console.log(week_start);
      console.log(week_end);
      sxWidth =
        ((week_end.getTime() - week_start.getTime()) / 60000) *
        (hourWidth / 60);
    }
  }
  return { sxLeft: sxLeft, sxWidth: sxWidth };
};

// デモ用シフト時間決め打ち
const shiftTime = [
  { name: "田中", shiftStart: 11, shiftEnd: 23 },
  { name: "平山", shiftStart: 11, shiftEnd: 21 },
  { name: "佐々木", shiftStart: 8, shiftEnd: 13 },
  { name: "森本", shiftStart: 13, shiftEnd: 20 },
  { name: "遠藤", shiftStart: 13, shiftEnd: 20 },
  { name: "岡崎", shiftStart: 21, shiftEnd: 27 },
  { name: "大澤", shiftStart: 21, shiftEnd: 27 },
  { name: "佐々木", shiftStart: 26, shiftEnd: 33 },
  { name: "林", shiftStart: 26, shiftEnd: 33 },
  // { name: "Aさん", shiftStart: 26, shiftEnd: 33 },
];

export default function Weekly2() {
  const { selectedDate, setSelectedDate, weeklyTasks } = useTasks();
  const navigate = useNavigate();

  const calendarTasks = weeklyTasks;
  const { assignedNames, nameGroupedTask, cardPositions } = useTaskGrouping({
    calendarTasks,
    selectedDate,
    shiftTime,
    calcCardPos,
  });

  console.log(calendarTasks);

  const getWeekStart = (date: Dayjs) => date.weekday(1); // 月曜日

  const handleDay = (handle_date: Dayjs) => {
    setSelectedDate(handle_date);
    navigate("/");

    console.log(handle_date);
  };

  const categoryColor = (category: string | undefined) => {
    if (!category) return "white";
    switch (category) {
      case "買い場維持":
        return "#E3F2FD";
      case "お客様対応":
        return "#E8F5E9";
      case "定時固定業務":
        return "#FFF8E1";
      default:
        return "white";
    }
  };
  return (
    <Box
      sx={{
        display: "flex",
        maxWidth: "100%",
        maxHeight: "calc(100vh - 160px)",
      }}
    >
      <Box
        sx={{
          flexBasis: "70%",
          flexGrow: 0,
          maxWidth: "90%",
          margin: 1,
        }}
      >
        <Stack
          direction={"row"}
          sx={{
            border: "1px gray solid",
            overflow: "visible",
          }}
        >
          <Stack direction={"column"} spacing={0}>
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                height: 50,
              }}
            />
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                height: 50,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              割当済/勤務予定
            </Box>
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                height: 50,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              従業員
            </Box>
            {assignedNames.map((name) => (
              <Box
                sx={{
                  width: "100%",
                  height: rowHeight,
                  boxSizing: "border-box",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                }}
              >
                {name}
              </Box>
            ))}
          </Stack>
          <Stack
            direction={"column"}
            spacing={0}
            sx={{
              flexGrow: 1,
              overflowX: "auto",
              width: "100%",
              position: "relative",
            }}
          >
            <Box sx={{ width: "max-content", position: "relative" }}>
              <Stack direction={"row"} spacing={0}>
                {["月", "火", "水", "木", "金", "土", "日"].map(
                  (day, index) => {
                    const handle_date = getWeekStart(selectedDate).add(
                      index,
                      "day"
                    );
                    return (
                      <Button
                        key={index}
                        sx={{
                          border: "1px rgba(25, 118, 210, 0.08) solid",
                          boxSizing: "border-box",
                          height: 50,
                          width: dayWidth,
                          flexShrink: 0,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: "#F8FCFF",
                          borderRadius: "3px",
                          color: "black",
                          fontSize: "18px",
                          boxShadow:
                            "0px 2px 1px 0px rgba(152, 122, 122, 0.5), inset 0px -3px 6px -2px rgba(0, 0, 0, 0.3)",
                        }}
                        onClick={() => handleDay(handle_date)}
                      >
                        <Typography>
                          {handle_date.format("M/D")}({day})
                        </Typography>
                      </Button>
                    );
                  }
                )}
              </Stack>
            </Box>
            <Box sx={{ width: "max-content", position: "relative" }}>
              <Stack direction={"row"} spacing={0}>
                {[
                  "60/90人時",
                  "65/85人時",
                  "62/90人時",
                  "65/85人時",
                  "70/90人時",
                  "91/100人時",
                  "88/100人時",
                ].map((workTime, index) => {
                  return (
                    <Box
                      key={index}
                      sx={{
                        border: "1px rgba(25, 118, 210, 0.08) solid",
                        boxSizing: "border-box",
                        height: 50,
                        width: dayWidth,
                        flexShrink: 0,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {workTime}
                    </Box>
                  );
                })}
              </Stack>
            </Box>
            <Box sx={{ width: "max-content", position: "relative" }}>
              <Stack direction={"row"} spacing={0}>
                {Array(14)
                  .fill(0)
                  .map((_, index) => {
                    return (
                      <Box
                        key={index}
                        sx={{
                          border: "1px rgba(25, 118, 210, 0.08) solid",
                          boxSizing: "border-box",
                          height: 50,
                          width: dayWidth / 2,
                          flexShrink: 0,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        {index % 2 == 0 ? "AM" : "PM"}
                      </Box>
                    );
                  })}
              </Stack>
            </Box>
            <Box sx={{ width: "fit-content", position: "relative" }}>
              {assignedNames.map((name) => {
                return (
                  <Box
                    sx={{
                      width: dayWidth * 7,
                      border: 0,
                      boxSizing: "border-box",
                      height: rowHeight,
                      position: "relative",
                      backgroundImage: `url("${bgSvg}")`,
                      backgroundColor: `rgb(239, 239, 239)`,
                      backgroundSize: `${hourWidth}px ${rowHeight}px, 100% 100%, 100% 100%, 100% 100%`,
                      backgroundRepeat: `repeat, no-repeat, no-repeat, no-repeat`,
                      backgroundClip: "border-box",
                    }}
                  >
                    {nameGroupedTask[name]?.map((item) => {
                      const cardPos = cardPositions[item.cardId];
                      return (
                        <Box
                          sx={{
                            position: "absolute",
                            left: cardPos?.sxLeft,
                            width: cardPos?.sxWidth,
                            height: rowHeight - 2,
                            border: "1px solid gray",
                            backgroundColor: categoryColor(item.category),
                          }}
                        ></Box>
                      );
                    })}
                  </Box>
                );
              })}
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}
