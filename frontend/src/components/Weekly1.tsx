// 1. React相関HOOKS
import { useNavigate } from "react-router-dom";

// 2. UIフレームワーク系, 外部ライブラリ
import { Box, Stack, Button, Typography } from "@mui/material";
import dayjs, { Dayjs } from "dayjs";
import weekday from "dayjs/plugin/weekday";
import isSameOrAfter from "dayjs/plugin/weekday";
import isSameOrBefore from "dayjs/plugin/weekday";
import isBetween from "dayjs/plugin/weekday";

// 3. オリジナル，内部ライブラリ
import "../App.css";
import { useTasks } from "../utils/TaskContext";

dayjs.extend(weekday);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(isBetween);

declare module "dayjs" {
  interface Dayjs {
    isSameOrAfter(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
    isSameOrBefore(date: dayjs.ConfigType, unit?: dayjs.OpUnitType): boolean;
    isBetween(
      a: dayjs.ConfigType,
      b: dayjs.ConfigType,
      unit?: dayjs.OpUnitType,
      inclusivity?: string,
    ): boolean;
  }
}

const rowHeight = 80;
const hourWidth = 12;
const dayWidth = hourWidth * 24;

const getBackgroundColor = (category: string) => {
  switch (category) {
    case "買い場維持":
      return "#E3F2FD";
    case "お客様対応":
      return "#E8F5E9";
    case "定時固定":
      return "#FFF8E1";
    default:
      return "white";
  }
};

const getCategoryOrder = (category: string) => {
  switch (category) {
    case "買い場維持":
      return 1;
    case "お客様対応":
      return 2;
    case "定時固定":
      return 3;
    default:
      return 4;
  }
};

const shiftTimes = [
  {
    day: 0,
    name: "田中",
    shiftStart: 900,
    shiftEnd: 1800,
  },
  {
    day: 0,
    name: "佐藤",
    shiftStart: 1300,
    shiftEnd: 2200,
  },
  {
    day: 1,
    name: "田中",
    shiftStart: 930,
    shiftEnd: 1800,
  },
  {
    day: 1,
    name: "鈴木",
    shiftStart: 1400,
    shiftEnd: 2300,
  },
  {
    day: 2,
    name: "佐藤",
    shiftStart: 900,
    shiftEnd: 1800,
  },
  {
    day: 2,
    name: "田中",
    shiftStart: 1200,
    shiftEnd: 2100,
  },
  {
    day: 3,
    name: "鈴木",
    shiftStart: 930,
    shiftEnd: 1800,
  },
  {
    day: 3,
    name: "山田",
    shiftStart: 1500,
    shiftEnd: 2400,
  },
  {
    day: 4,
    name: "田中",
    shiftStart: 900,
    shiftEnd: 1800,
  },
  {
    day: 4,
    name: "佐藤",
    shiftStart: 1300,
    shiftEnd: 2200,
  },
  {
    day: 5,
    name: "鈴木",
    shiftStart: 800,
    shiftEnd: 1800,
  },
  {
    day: 5,
    name: "山田",
    shiftStart: 1200,
    shiftEnd: 2200,
  },
  {
    day: 6,
    name: "田中",
    shiftStart: 800,
    shiftEnd: 1800,
  },
  {
    day: 6,
    name: "佐藤",
    shiftStart: 1300,
    shiftEnd: 2300,
  },
];

export default function Weekly1() {
  const { selectedDate, setSelectedDate, weeklyTasks } = useTasks();
  const navigate = useNavigate();

  const getWeekStart = (date: Dayjs) => date.weekday(1); // 月曜日

  const handleDay = (handle_date: Dayjs) => {
    setSelectedDate(handle_date);
    navigate("/");

    console.log(handle_date);
  };

  const getDailyShiftHours = (day: number) => {
    return shiftTimes.reduce((sum, shift) => {
      if (shift.day === day) {
        const startHour = Math.floor(shift.shiftStart / 100);
        const startMinute = shift.shiftStart % 100;
        const endHour = Math.floor(shift.shiftEnd / 100);
        const endMinute = shift.shiftEnd % 100;

        const start = dayjs().hour(startHour).minute(startMinute);
        let end = dayjs().hour(endHour).minute(endMinute);

        if (end.isBefore(start)) {
          end = end.add(1, "day");
        }

        const hours = end.diff(start, "minute") / 60;
        return sum + hours;
      }
      return sum;
    }, 0);
  };

  const getCategoryHours = (targetCategory: string) => {
    const totalMinutes = weeklyTasks.reduce((sum, task) => {
      if (task.scheduledTime && task.category === targetCategory) {
        return sum + task.scheduledTime;
      }
      return sum;
    }, 0);

    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  const getTotalHours = () => {
    const totalMinutes = weeklyTasks.reduce((sum, task) => {
      if (task.scheduledTime) {
        return sum + task.scheduledTime;
      }
      return sum;
    }, 0);
    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  const getOtherCategoryHours = () => {
    const totalMinutes = weeklyTasks.reduce((sum, task) => {
      if (
        task.scheduledTime &&
        task.category !== "買い場維持" &&
        task.category !== "お客様対応" &&
        task.category !== "定時固定"
      ) {
        return sum + task.scheduledTime;
      }
      return sum;
    }, 0);
    return Math.round((totalMinutes / 60) * 10) / 10;
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        maxWidth: "100%",
        maxHeight: "calc(100vh - 160px)",
      }}
    >
      <Box sx={{ margin: 1, marginBottom: 2 }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Box
            sx={{
              border: "1px solid #ccc",
              padding: 1,
              minWidth: 120,
              textAlign: "center",
            }}
          >
            <Box sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>合計</Box>
            <Box sx={{ fontSize: "0.875rem" }}>{getTotalHours()} h/週</Box>
          </Box>

          <Box
            sx={{
              border: "1px solid #ccc",
              padding: 1,
              minWidth: 120,
              textAlign: "center",
              backgroundColor: getBackgroundColor("買い場維持"),
            }}
          >
            <Box sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
              買い場維持業務
            </Box>
            <Box sx={{ fontSize: "0.875rem" }}>
              {getCategoryHours("買い場維持")} h/週
            </Box>
          </Box>

          <Box
            sx={{
              border: "1px solid #ccc",
              padding: 1,
              minWidth: 120,
              textAlign: "center",
              backgroundColor: getBackgroundColor("お客様対応"),
            }}
          >
            <Box sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
              お客様対応業務
            </Box>
            <Box sx={{ fontSize: "0.875rem" }}>
              {getCategoryHours("お客様対応")} h/週
            </Box>
          </Box>

          <Box
            sx={{
              border: "1px solid #ccc",
              padding: 1,
              minWidth: 120,
              textAlign: "center",
              backgroundColor: getBackgroundColor("定時固定"),
            }}
          >
            <Box sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>
              定時固定業務
            </Box>
            <Box sx={{ fontSize: "0.875rem" }}>
              {getCategoryHours("定時固定")} h/週
            </Box>
          </Box>

          <Box
            sx={{
              border: "1px solid #ccc",
              padding: 1,
              minWidth: 120,
              textAlign: "center",
              backgroundColor: getBackgroundColor("その他"),
            }}
          >
            <Box sx={{ fontWeight: "bold", fontSize: "0.875rem" }}>その他</Box>
            <Box sx={{ fontSize: "0.875rem" }}>
              {getOtherCategoryHours()} h/週
            </Box>
          </Box>
        </Stack>
      </Box>
      <Box
        sx={{
          flexBasis: "70%",
          flexGrow: 0,
          maxWidth: "90%",
          margin: 1,
        }}
      >
        <Stack
          direction={"row"}
          sx={{
            border: "1px gray solid",
            overflow: "visible",
          }}
        >
          {/* １列目 */}
          <Stack direction={"column"} spacing={0}>
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                height: 50,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              曜日
            </Box>
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                height: 50,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              シフト時間
            </Box>
            <Box
              sx={{
                width: 150,
                borderBottom: "1px rgba(25, 118, 210, 0.08) solid",
                boxSizing: "border-box",
                flexGrow: 1,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              作業予定
            </Box>
          </Stack>
          {/* ２列目 */}
          <Stack
            direction={"column"}
            spacing={0}
            sx={{
              flexGrow: 1,
              overflowX: "auto",
              width: "100%",
              position: "relative",
            }}
          >
            <Box sx={{ width: "max-content", position: "relative" }}>
              <Stack direction={"row"} spacing={0}>
                {["月", "火", "水", "木", "金", "土", "日"].map(
                  (day, index) => {
                    const handle_date = getWeekStart(selectedDate).add(
                      index,
                      "day",
                    );
                    return (
                      <Button
                        key={index}
                        sx={{
                          border: "1px rgba(25, 118, 210, 0.08) solid",
                          boxSizing: "border-box",
                          height: 50,
                          width: dayWidth,
                          flexShrink: 0,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                          backgroundColor: "#F8FCFF",
                          borderRadius: "3px",
                          color: "black",
                          fontSize: "18px",
                          boxShadow:
                            "0px 2px 1px 0px rgba(152, 122, 122, 0.5), inset 0px -3px 6px -2px rgba(0, 0, 0, 0.3)",
                        }}
                        onClick={() => handleDay(handle_date)}
                      >
                        <Typography>
                          {handle_date.format("M/D")}({day})
                        </Typography>
                      </Button>
                    );
                  },
                )}
              </Stack>
            </Box>
            <Box sx={{ width: "max-content", position: "relative" }}>
              <Stack direction={"row"} spacing={0}>
                {[0, 1, 2, 3, 4, 5, 6].map((weekday) => {
                  const dailyShiftHours = getDailyShiftHours(weekday);
                  return (
                    <Box
                      key={weekday}
                      sx={{
                        border: "1px rgba(25, 118, 210, 0.08) solid",
                        boxSizing: "border-box",
                        height: 50,
                        width: dayWidth,
                        flexShrink: 0,
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      {Math.round(dailyShiftHours * 10) / 10} h
                    </Box>
                  );
                })}
              </Stack>
            </Box>
            <Box sx={{ width: "fit-content", position: "relative" }}>
              <Stack direction="row" spacing={0}>
                {[0, 1, 2, 3, 4, 5, 6].map((weekday) => {
                  const dailyShiftHours = getDailyShiftHours(weekday);
                  const shiftHeight = dailyShiftHours * rowHeight;
                  const dailyTasks = weeklyTasks
                    .filter((task) => {
                      if (!task.start) return false;
                      const taskWeekday = dayjs(task.start).weekday();
                      const adjustedWeekday =
                        taskWeekday === 0 ? 6 : taskWeekday - 1;
                      return adjustedWeekday === weekday;
                    })
                    .sort((a, b) => {
                      const orderA = getCategoryOrder(a.category ?? "");
                      const orderB = getCategoryOrder(b.category ?? "");
                      return orderA - orderB;
                    });

                  return (
                    <Box
                      key={weekday}
                      sx={{
                        width: dayWidth,
                        boxSizing: "border-box",
                        position: "relative",
                        minHeight: shiftHeight,
                        backgroundImage: `linear-gradient(to bottom, #f5f5f5 0%, #f5f5f5 ${shiftHeight}px, transparent ${shiftHeight}px)`,
                        border: "1px solid #e0e0e0",
                        borderRadius: 1,
                      }}
                    >
                      {dailyTasks.map((task) => {
                        const taskHeight =
                          ((task.scheduledTime || 0) / 60) * rowHeight;
                        return (
                          <Box
                            key={task.taskId}
                            sx={{
                              width: "100%",
                              border: "1px solid rgba(25, 118, 210, 0.3)",
                              borderRadius: 1,
                              boxSizing: "border-box",
                              height: taskHeight,
                              minHeight: 40,
                              padding: 1,
                              backgroundColor: getBackgroundColor(
                                task.category ?? "",
                              ),
                              display: "flex",
                              flexDirection: "column",
                              justifyContent: "space-between",
                              boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
                            }}
                          >
                            <Box sx={{ fontWeight: "bold" }}>
                              {task.taskName}
                            </Box>
                          </Box>
                        );
                      })}
                    </Box>
                  );
                })}
              </Stack>
            </Box>
          </Stack>
        </Stack>
      </Box>
    </Box>
  );
}
