import { Box, styled, TextField } from "@mui/material";
import { SetStateAction, useState } from "react";

const RedTextField = styled(TextField)(() => ({
  "& .MuiInputBase-input": {
    color: "red",
  },
}));

const getNotification = () => {
  const text = `5/2予定の納品に遅れ（8:42更新）`;
  return text;
};
export default function NotificationArea() {
  const [notification, setNotification] = useState(getNotification());

  const handleTextFieldChange = (event: {
    target: { value: SetStateAction<string> };
  }) => {
    setNotification(event.target.value);
  };

  return (
    <Box sx={{ ml: "auto" }}>
      <RedTextField
        id="notification_textfield"
        value={notification}
        onChange={handleTextFieldChange}
        // maxWidthは幅を詰めた時に日付とMDの分の幅を引いた概算の値
        sx={{
          my: 1,
          width: "50vw",
          maxWidth: "calc(100vw - 450px)",
        }}
      />
    </Box>
  );
}
