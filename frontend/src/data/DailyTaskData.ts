// import { Task } from "../types/task";

// const tasks: Task[] = [];
// //   {
// //     id: 1,
// //     GP: "菓子",
// //     dueDate: "2024-11-3",
// //     assignedDate: "2024-11-2",
// //     summary: "情熱むき栗70gx3回収",
// //     category: "商品回収",
// //     teamsLink: "🔗",
// //     implementationDate: "2024-11-3",
// //     assignedMember: "岡田",
// //     status: "報告完了",
// //   },
// //   {
// //     id: 2,
// //     GP: "菓子・珍味",
// //     dueDate: "2024-11-10",
// //     assignedDate: "2024-11-30",
// //     summary: "フリー吸い上げ、数量記入",
// //     category: "吸い上げ",
// //     teamsLink: "🔗",
// //     implementationDate: "2024-11-3",
// //     assignedMember: "長瀬",
// //     status: "確認依頼",
// //   },
// //   {
// //     id: 3,
// //     GP: "パーディーグッズ",
// //     dueDate: "2024-11-15",
// //     assignedDate: "2024-11-03",
// //     summary: "季節商品の在庫状況の確認・追加発注",
// //     category: "在庫管理",
// //     teamsLink: "🔗",
// //     implementationDate: "2024-11-3",
// //     assignedMember: "水野",
// //     status: "担当割当・進行中",
// //   },
// // ];

// export default tasks;
