type StoreMapping = {
  [key: string]: string;
};

export const STORE_MAPPING: StoreMapping = {
  "694": "田無駅前", //この店はいただいたリストになかったが、通達内にある写真にあった。（調べたら2024年11月21日にオープンした新しい店舗だった。
  "195": "M西帯広",
  "222": "中標津",
  "357": "釧路",
  "177": "M旭川",
  "221": "小樽",
  "268": "DQ小樽",
  "328": "<PERSON>新川",
  "547": "M篠路",
  "42": "手稲",
  "45": "平岡",
  "145": "北42条",
  "665": "厚別",
  "152": "函館七重浜",
  "199": "M室蘭中島",
  "229": "M函館",
  "236": "M苫小牧",
  "267": "DQ室蘭中島店",
  "154": "青森観光通り",
  "231": "八戸",
  "264": "DQ八戸",
  "318": "弘前",
  "618": "五所川原",
  "675": "Mガーラタウン青森",
  "687": "むつ",
  "286": "盛岡上堂",
  "349": "石巻街道矢本",
  "610": "一関",
  "648": "古川",
  "197": "M秋田",
  "353": "Mル・パーク三川",
  "477": "M横手",
  "601": "潟上",
  "125": "M仙台台原",
  "386": "M仙台富谷",
  "640": "利府",
  "113": "仙台南",
  "160": "晩翠通り",
  "167": "六丁の目",
  "503": "仙台駅西口本",
  "135": "郡山駅東",
  "304": "山形嶋南",
  "516": "須賀川",
  "607": "福島",
  "652": "米沢",
  "192": "M上水戸",
  "193": "Mラパークいわき",
  "211": "M勝田",
  "269": "DQ勝田店",
  "196": "M黒磯",
  "546": "MUNY会津若松",
  "600": "大田原",
  "237": "M桐生",
  "487": "MUNY伊勢崎東",
  "491": "UNY藤岡",
  "536": "MUNY本庄",
  "83": "土浦北",
  "174": "水戸",
  "362": "Mつくば",
  "416": "M日立",
  "293": "M龍ケ崎",
  "380": "M神栖",
  "424": "境大橋",
  "497": "MUNY佐原東",
  "65": "宇都宮簗瀬",
  "235": "M宇都宮",
  "439": "下館",
  "440": "小山駅前",
  "540": "アピタ宇都宮",
  "579": "栃木平柳",
  "78": "高崎",
  "81": "伊勢崎",
  "291": "ガーデン前橋",
  "597": "吉岡",
  "681": "太田",
  "33": "川口新井宿",
  "34": "蕨",
  "226": "西川口駅前",
  "277": "エッセンス川口駅前",
  "365": "越谷",
  "625": "M武蔵浦和",
  "63": "北池袋",
  "73": "練馬",
  "394": "M板橋志村",
  "500": "池袋駅西口",
  "504": "ピカソ大塚北口駅前",
  "662": "赤羽東口",
  "7": "大宮",
  "148": "与野",
  "334": "北上尾PAPA",
  "340": "大宮東口",
  "602": "行田持田インター",
  "62": "新座野火止",
  "143": "川越",
  "370": "M東松山",
  "617": "川越東口",
  "669": "鶴ヶ島",
  "70": "Mかしわ",
  "156": "千葉ニュータウン",
  "371": "M成田",
  "626": "セブンパークアリオ柏",
  "637": "コスメお菓子驚辛",
  "4": "木更津",
  "20": "千葉中央",
  "108": "君津",
  "633": "稲毛長沼",
  "646": "コスメユニモちはら台",
  "698": "館山",
  "5": "幕張",
  "6": "浜野",
  "239": "船橋南口",
  "509": "M習志野",
  "661": "千葉ポートタウン",
  "9": "葛西",
  "18": "原木西船橋",
  "96": "ピカソ環七江戸川",
  "121": "行徳駅前",
  "392": "ピカソ南行徳駅前",
  "170": "M四街道",
  "212": "M成東",
  "337": "茂原",
  "339": "旭",
  "528": "MUNY市原",
  "168": "柏駅前",
  "203": "ラパーク瑞江",
  "209": "M本八幡",
  "428": "M八千代16号",
  "172": "M三郷",
  "227": "M浦和原山",
  "261": "M草加",
  "660": "M成増",
  "155": "M春日部",
  "173": "M北鴻巣",
  "215": "M蓮田",
  "495": "UNY大桑",
  "35": "ピカソ新小岩",
  "53": "青戸",
  "327": "ピカソ小岩駅前",
  "343": "後楽園",
  "635": "錦糸町北口",
  "94": "竹ノ塚",
  "431": "M環七梅島",
  "468": "西新井駅前",
  "628": "北千住西口",
  "677": "鶯谷",
  "276": "高田馬場駅前",
  "289": "ピカソ赤坂",
  "378": "新宿明治通り",
  "502": "新大久保駅前",
  "632": "お菓子お酒",
  "71": "ピカソ三軒茶屋",
  "208": "中目黒本",
  "368": "プラチナ白金台",
  "508": "ピカソ目黒駅前",
  "606": "五反田東口",
  "655": "キラダイバーシティ東京",
  "61": "中野駅前",
  "262": "荻窪駅前",
  "321": "吉祥寺駅前",
  "659": "西友吉祥寺",
  "692": "調布駅前",
  "10": "環八世田谷",
  "25": "環七方南町",
  "314": "ピカソ桜上水",
  "624": "下北沢",
  "1": "府中",
  "16": "東八三鷹",
  "37": "ピカソ国分寺",
  "297": "エッセンス関町",
  "325": "M東久留米",
  "30": "小平",
  "126": "東所沢",
  "151": "DQ入間",
  "157": "所沢宮本町",
  "354": "多摩瑞穂",
  "28": "東名横浜インター",
  "58": "町田駅前",
  "171": "溝ノ口駅前",
  "391": "M青葉台",
  "14": "京王堀之内",
  "22": "めじろ台",
  "150": "M古淵",
  "292": "M上鶴間",
  "338": "SING橋本駅前",
  "409": "大和",
  "48": "M川崎",
  "407": "M大森山王",
  "421": "MEGAドン・キホーテ大森山王店（酒）",
  "513": "M港山下総本",
  "214": "二俣川",
  "444": "M東名川崎",
  "471": "MUNY横浜大口",
  "220": "八王子駅前",
  "234": "小金井店",
  "315": "M武蔵小金井駅前",
  "388": "M立川",
  "398": "みちくさ屋_八王子駅前店",
  "518": "ららぽーと立川立飛",
  "225": "小田原",
  "385": "M綾瀬",
  "473": "MUNY座間",
  "60": "BIGFUN平和島",
  "216": "蒲田駅前",
  "488": "M鶴見中央",
  "510": "ピカソ川崎銀柳街",
  "672": "京急蒲田",
  "122": "横浜西口",
  "344": "M新横浜",
  "347": "伊勢佐木町",
  "679": "キラドンキ横浜",
  "26": "横須賀",
  "319": "M狩場インター",
  "374": "ピカソ横須賀中央",
  "464": "日野インター",
  "56": "戸塚原宿",
  "68": "平塚",
  "166": "M厚木",
  "169": "OUTLET館",
  "201": "相模の國の駅あつぎ産直館",
  "412": "藤沢駅南口",
  "501": "ピカソ大船",
  "559": "M秦野",
  "79": "新潟駅南",
  "282": "長岡インター",
  "455": "長岡川崎",
  "569": "アピタ新潟亀田",
  "616": "新発田",
  "680": "燕",
  "176": "M柏崎",
  "306": "M上越インター",
  "627": "十日町",
  "120": "川中島",
  "198": "M長野",
  "280": "上田",
  "453": "長野駅前",
  "622": "信州中野",
  "691": "佐久平",
  "129": "南松本",
  "448": "茅野",
  "545": "MUNY高森",
  "577": "MUNY伊那",
  "688": "安曇野インター",
  "141": "河口湖インター",
  "520": "M甲府",
  "557": "MUNY石和",
  "117": "沼津",
  "445": "M伊東",
  "480": "MUNY中里",
  "483": "UNY富士中央",
  "531": "MUNY富士吉原",
  "74": "SBS通り",
  "109": "静岡両替町",
  "387": "山崎",
  "515": "ミチNEOPASA 清水",
  "684": "新静岡駅前",
  "696": "清水",
  "400": "藤枝",
  "562": "MUNY榛原",
  "570": "MUNY大覚寺",
  "300": "M袋井",
  "390": "磐田",
  "537": "MUNY掛川",
  "149": "富山",
  "310": "高岡",
  "530": "MUNY魚津",
  "539": "MUNY砺波",
  "631": "七尾",
  "668": "射水",
  "200": "Mラパーク金沢",
  "288": "小松",
  "406": "金沢森本",
  "430": "M金沢鞍月",
  "312": "福井大和田",
  "514": "越前武生インター",
  "523": "MUNY福井",
  "550": "MUNY敦賀",
  "186": "M浜松可美",
  "358": "M浜松三方原",
  "492": "MUNY浜松泉町",
  "699": "浜松",
  "451": "M豊橋",
  "459": "LOSS&HONEY_MEGA豊橋店",
  "460": "Mクラスポ蒲郡",
  "476": "MUNY国府",
  "542": "MUNY吉良",
  "181": "豊田",
  "283": "M豊田本",
  "330": "M岡崎",
  "475": "MUNY豊田元町",
  "565": "MUNY矢作",
  "478": "UNY可児",
  "521": "MUNY美濃加茂",
  "525": "MUNY岐阜",
  "533": "MUNY恵那",
  "184": "M鵜沼",
  "299": "M岐阜瑞穂",
  "457": "M関マーゴ",
  "505": "大垣インター",
  "484": "MUNY気噴",
  "522": "MUNY桃花台",
  "541": "MUNY香久山",
  "564": "アピタ長久手",
  "163": "M名古屋本",
  "524": "MUNY小牧",
  "526": "MUNY大口",
  "549": "MUNY江南",
  "560": "アピタ新守山",
  "88": "楽市街道名古屋",
  "182": "一宮",
  "352": "M春日井",
  "397": "M千種香流",
  "76": "中川山王",
  "180": "緑",
  "335": "今池",
  "408": "M名四丹後通り",
  "615": "大須",
  "667": "キラ近鉄パッセ",
  "472": "MUNY東海通",
  "532": "MUNY納屋橋",
  "538": "MUNYアラタマ",
  "97": "M新安城",
  "183": "刈谷",
  "417": "M東海名和",
  "534": "MUNY豊明",
  "124": "半田",
  "494": "MUNY太田川",
  "498": "MUNY武豊",
  "558": "UNY碧南",
  "481": "MUNY伝法寺",
  "485": "MUNY一宮大和",
  "529": "MUNY稲沢東",
  "474": "MUNY星川",
  "490": "MUNY勝幡",
  "556": "UNY十四山",
  "161": "鈴鹿",
  "298": "M四日市",
  "563": "アピタ四日市",
  "580": "MUNY嬉野",
  "395": "伊勢",
  "441": "M津桜橋",
  "486": "MUNY鈴鹿",
  "499": "M伊勢上地",
  "165": "草津店",
  "296": "M長浜",
  "403": "M大津",
  "436": "M豊郷",
  "674": "彦根",
  "57": "京都南インター",
  "266": "M宇治",
  "410": "京都洛西",
  "454": "M山科",
  "102": "奈良",
  "364": "香芝インター",
  "379": "M桜井",
  "399": "天理",
  "271": "高槻",
  "341": "M茨木",
  "372": "M箕面",
  "506": "香里園",
  "614": "枚方",
  "685": "寝屋川",
  "75": "住之江公園",
  "87": "石切",
  "95": "八尾",
  "324": "M富田林",
  "673": "ドミセアリオ八尾",
  "86": "上本町",
  "350": "M深江橋",
  "609": "天満駅",
  "629": "寺田町駅",
  "77": "羽曳野",
  "91": "和泉",
  "127": "新金岡",
  "413": "アクロスモール泉北",
  "535": "M和泉中央",
  "683": "上野芝（仮）",
  "119": "ぶらくり丁",
  "164": "泉佐野",
  "308": "M紀の川",
  "375": "M和歌山次郎丸",
  "676": "貝塚",
  "479": "MUNY近江八幡",
  "482": "MUNY東近江",
  "568": "M水口",
  "493": "MUNY名張",
  "496": "MUNY西大和",
  "548": "MUNY精華台",
  "175": "M弁天町",
  "230": "岸和田",
  "273": "MDQ岸和田",
  "360": "M松原",
  "115": "桜ノ宮",
  "303": "大日",
  "367": "法円坂",
  "463": "江坂",
  "599": "十三",
  "651": "京橋",
  "40": "伊丹",
  "333": "三田",
  "366": "豊中",
  "415": "川西",
  "425": "M福知山",
  "64": "三宮",
  "80": "西宮",
  "270": "M神戸本",
  "307": "神戸西",
  "446": "M神戸学園都市",
  "664": "三宮オーパセンター街",
  "99": "加古川",
  "159": "姫路RIOS",
  "294": "M姫路白浜",
  "466": "M姫路広畑",
  "384": "M出雲",
  "429": "鳥取本",
  "682": "M米子",
  "133": "倉敷",
  "139": "岡山下中野",
  "189": "福山",
  "389": "岡山駅前",
  "630": "M松永",
  "104": "高松",
  "316": "丸亀",
  "576": "M徳島",
  "654": "高松丸亀町",
  "693": "小松島",
  "188": "松山",
  "204": "高知ヴィアン",
  "313": "M西条玉津",
  "527": "今治",
  "603": "四国中央",
  "620": "松山大街道",
  "697": "高知",
  "105": "広島祗園",
  "158": "宇部",
  "281": "M宇品",
  "301": "広島八丁堀",
  "305": "下関長府",
  "90": "DPlaza大分",
  "427": "M大分光吉インター",
  "650": "別府",
  "686": "大分中央町",
  "100": "黒崎",
  "153": "小倉",
  "369": "コスタ行橋",
  "645": "小倉魚町",
  "663": "サイドモール小倉",
  "38": "楽市街道箱崎",
  "41": "西新",
  "309": "M飯塚",
  "611": "鞍手",
  "644": "宗像",
  "690": "キラドンキ博多",
  "426": "今宿",
  "437": "M那珂川",
  "573": "M福重",
  "656": "博多駅南",
  "50": "楽市楽座久留米",
  "207": "佐賀",
  "393": "M筑紫野インター",
  "456": "八女",
  "512": "西鉄久留米",
  "641": "唐津",
  "329": "M菊陽",
  "411": "南熊本",
  "517": "下通り",
  "519": "M八代",
  "634": "合志",
  "649": "荒尾",
  "178": "佐世保",
  "295": "浜町",
  "332": "M長崎時津",
  "396": "M大村インター",
  "217": "宮崎",
  "359": "M都城",
  "462": "M延岡",
  "619": "M宮崎橘通",
  "290": "鹿児島宇宿",
  "355": "天文館",
  "470": "M霧島隼人",
  "608": "M鹿屋",
  "658": "薩摩川内",
  "678": "鹿児島中央一番街",
  "302": "M宜野湾",
  "322": "Mうるま",
  "323": "国際通り",
  "420": "宮古島",
  "450": "M名護",
  "507": "石垣島",
  "598": "那覇壷川",
  "621": "M豊見城",
  "106": "情熱職人_久喜店",
  "405": "情熱職人_葛西店",
  "511": "情熱職人_東松山店",
  "636": "情熱職人_川越店",
  "638": "情熱職人_昭島店",
  "643": "情熱職人_霧が丘店",
  "8": "新宿",
  "29": "新宿歌舞伎町",
  "255": "池袋東口駅前",
  "449": "新宿東南口",
  "31": "六本木",
  "92": "銀座本館",
  "423": "ソラドンキ羽田空港",
  "442": "M渋谷本",
  "671": "ドミセ道玄坂",
  "98": "秋葉原",
  "107": "上野",
  "326": "浅草",
  "639": "御徒町",
  "555": "M札幌狸小路本",
  "574": "ソラドンキ新千歳空港",
  "657": "すすきの",
  "670": "キラキラドンキ狸小路",
  "348": "栄本",
  "623": "栄三丁目",
  "418": "京都アバンティ",
  "612": "四条河原町",
  "653": "京都烏丸七条",
  "110": "道頓堀",
  "373": "道頓堀御堂筋",
  "419": "なんば千日前",
  "275": "梅田本",
  "356": "M新世界",
  "381": "エキドンキエキマルシェ大阪",
  "438": "あべの天王寺駅前",
  "278": "中洲",
  "461": "福岡天神本",
};
