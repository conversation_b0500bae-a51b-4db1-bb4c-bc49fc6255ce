import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import LoginPage from "./pages/LoginPage";
import ProtectedRoute from "./utils/ProtectedRoute";
import TopPage from "./pages/TopPage";
import WeeklyPage from "./pages/WeeklyPage";
// import TopPage from "./pages/TopPage";

const Router = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/" element={<TopPage />} />
        <Route path="/weekly" element={<WeeklyPage />} />
        <Route element={<ProtectedRoute />}>
        </Route>

        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    </BrowserRouter>
  );
};

export default Router;
