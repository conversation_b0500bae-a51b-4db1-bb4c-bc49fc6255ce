import { useContext, useState } from "react";
import { signIn } from "../utils/authService";
import { useNavigate } from "react-router-dom";
import { Box, Button, Input, Stack } from "@mui/material";
import { UserContext } from "../utils/AccountContext";

const LoginPage = () => {
  const { username, setUsername } = useContext(UserContext);
  const [password, setPassword] = useState("");
  const navigate = useNavigate();

  const handleSignIn = async (e: { preventDefault: () => void }) => {
    e.preventDefault();
    try {
      const response = await signIn(username, password);
      if (response.username) {
        setUsername(response.username);
        navigate("/tasks");
      } else {
        console.error("Sign in failed: No username in response");
      }
    } catch (error) {
      alert(`Sign in failed: ${error}`);
    }
  };

  return (
    <Box
      className="loginForm"
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
      }}
    >
      <h1>Welcome</h1>
      <form onSubmit={handleSignIn}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Stack sx={{ mb: 4 }} spacing={0.5}>
            <div>
              <Input
                className="inputText"
                id="username"
                // type="email"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Username"
                required
              />
            </div>
            <Input
              className="inputText"
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              required
            />
          </Stack>
          <Button variant="outlined" type="submit">
            Sign In
          </Button>
        </Box>
      </form>
    </Box>
  );
};

export default LoginPage;
