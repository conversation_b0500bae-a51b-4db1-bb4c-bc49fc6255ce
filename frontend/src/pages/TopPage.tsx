import DailyView from "../components/DailyView";
import Layout from "../layouts/Layout";

// 認証関連のコードを残しています。必要なければ削除
//cognito認証により得られたTokenを整型
// function parseJwt(token: string) {
//   const base64Url = token.split(".")[1];
//   const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
//   const jsonPayload = decodeURIComponent(
//     window
//       .atob(base64)
//       .split("")
//       .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
//       .join("")
//   );
//   return JSON.parse(jsonPayload);
// }

const TopPage = () => {
  // const idToken = parseJwt(sessionStorage.idToken.toString());
  // const accessToken = parseJwt(sessionStorage.accessToken.toString());
  // console.log(
  //   `Amazon Cognito ID token encoded: ${sessionStorage.idToken.toString()}`
  // );
  // console.log("Amazon Cognito ID token decoded: ");
  // console.log(idToken);
  // console.log(
  //   `Amazon Cognito access token encoded: ${sessionStorage.accessToken.toString()}`
  // );
  // console.log("Amazon Cognito access token decoded: ");
  // console.log(accessToken);
  // console.log("Amazon Cognito refresh token: ");
  // console.log(sessionStorage.refreshToken);

  return (
    <Layout title="日次計画">
      <DailyView />
    </Layout>
  );
};

export default TopPage;
