import { useMemo, useState, useEffect } from "react";
import { Dayjs } from "dayjs";
import { CardTask } from "../types";

type UseTaskGroupingProps = {
  calendarTasks: CardTask[];
  selectedDate: Dayjs;
  shiftTime: { name: string }[];
  calcCardPos: (
    item: CardTask,
    selectedDate: Dayjs
  ) => { sxLeft: number; sxWidth: number };
};

export function useTaskGrouping({
  calendarTasks,
  selectedDate,
  shiftTime,
  calcCardPos,
}: UseTaskGroupingProps) {
  // 日付でタスクをフィルタ
  const currentDateTasks = calendarTasks;
  // 並び順
  const nameOrder = shiftTime.map((s) => s.name);

  // グルーピング
  const nameGroupedTask = useMemo(() => {
    const sorted = [...currentDateTasks].sort(
      (a, b) =>
        nameOrder.indexOf(a.assignedName ?? "") -
        nameOrder.indexOf(b.assignedName ?? "")
    );
    return sorted.reduce((acc, item) => {
      if (item.assignedName) {
        if (!acc[item.assignedName]) {
          acc[item.assignedName] = [];
        }
        acc[item.assignedName].push(item);
        return acc;
      } else {
        return acc;
      }
    }, {} as Record<string, CardTask[]>);
  }, [currentDateTasks, shiftTime]);

  const [assignedNames, setAssignedNames] = useState<string[]>(
    Object.keys(nameGroupedTask)
  );

  useEffect(() => {
    setAssignedNames(Object.keys(nameGroupedTask));
  }, [selectedDate, nameGroupedTask]);

  // カード位置
  const cardPositions = useMemo(() => {
    return currentDateTasks.reduce((acc, item) => {
      acc[item.cardId] = calcCardPos(item, selectedDate);
      return acc;
    }, {} as Record<string, { sxLeft: number; sxWidth: number }>);
  }, [currentDateTasks, selectedDate, calcCardPos]);

  return {
    currentDateTasks,
    nameGroupedTask,
    assignedNames,
    setAssignedNames,
    cardPositions,
  };
}
