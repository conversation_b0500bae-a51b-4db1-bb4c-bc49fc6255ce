import { useState } from "react";
import { DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import { BaseTask, CardTask, DragData } from "../types";
import { Dayjs } from "dayjs";
import { useTasks } from "../utils/TaskContext";
import { dataAlignerOnEdit } from "../utils/taskDataAlignment";

type UseDailyDnDProps = {
  currentDateTasks: CardTask[];
  assignedNames: string[];
  setAssignedNames: React.Dispatch<React.SetStateAction<string[]>>;
  setCalendarTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  backlogTasks: CardTask[];
  setBacklogTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  carryoverTasks: CardTask[];
  setCarryoverTasks: React.Dispatch<React.SetStateAction<CardTask[]>>;
  selectedDate: Dayjs;
  cardPositions: Record<string, { sxLeft: number; sxWidth: number }>;
  scrollContainerRef: React.RefObject<HTMLDivElement>;
  hourWidth: number;
};

const calcWorkedTIme = (start: Date, end: Date) => {
  const worked_time = Math.round(Math.abs(end.getTime() - start.getTime()));
  return worked_time;
};
// workedTime = historyの時間合計
const getWorkedTime = (task: BaseTask) => {
  if (task.history) {
    const sum_worked_time = task.history.reduce(
      (sum, record) => sum + calcWorkedTIme(record.start, record.end),
      0
    );
    return sum_worked_time;
  }
  return 0;
};
export const getRemainingTime = (task: BaseTask): number => {
  const worked_time = getWorkedTime(task);
  const scheduledMs = (task?.scheduledTime ?? 0) * 60 * 1000;
  if (worked_time != null) {
    return scheduledMs - worked_time;
  }
  return scheduledMs;
};

export function useDailyDnD({
  currentDateTasks,
  assignedNames,
  setAssignedNames,
  backlogTasks,
  carryoverTasks,
  selectedDate,
  cardPositions,
  scrollContainerRef,
  hourWidth,
}: UseDailyDnDProps) {
  const { tasks, updateTask } = useTasks();
  const [activeItem, setActiveItem] = useState<DragData | null>(null);
  const [activeBacklogItem, setActiveBacklogItem] = useState<DragData | null>(
    null
  );
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStartLeft, setDragStartLeft] = useState<number | null>(null);

  // 時間を計算する
  const calcTimeFromLeft = (leftPx: number, selectedDate: Dayjs): Date => {
    const totalHours = leftPx / hourWidth;
    const hours = Math.floor(totalHours);
    const quarter = Math.round((totalHours - hours) * 4);
    const minutes = quarter * 15;
    let base = selectedDate.toDate();
    if (hours < 3) {
      base = selectedDate.add(-1, "d").toDate();
    } else if (hours > 27) {
      base = selectedDate.add(1, "d").toDate();
    }
    base.setHours((21 + hours) % 24);
    base.setMinutes(minutes);
    base.setSeconds(0);
    base.setMilliseconds(0);
    return base;
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    console.log(active);
    const active_type = active.data.current?.type;
    console.log(`${active.id}から${over?.id}に移動`);
    if (active_type == "row") {
      if (!over || active.id === over.id) return;
      const oldIndex = assignedNames.indexOf(active.id as string);
      const newIndex = assignedNames.indexOf(over.id as string);
      if (oldIndex === -1 || newIndex === -1) return;
      setAssignedNames((prevRows) => {
        const arr = [...prevRows];
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, active.id as string);
        return arr;
      });
    } else if (
      activeBacklogItem?.from == "carryover" &&
      over?.id != "backlog" &&
      over?.id != "carryover"
    ) {
      // 繰り越し→カレンダー
      setTimeout(() => {
        setActiveBacklogItem(null);
        setIsDragging(false);
      }, 0);

      const cardId = active.id;
      const task = carryoverTasks?.find((task) => task.cardId == cardId);

      const movedTo = event.active.rect.current.translated;
      const translateToLeft = (dragStartLeft ?? 0) + (movedTo?.left ?? 0);
      const scrollLeft = scrollContainerRef.current?.scrollLeft ?? 0;
      const calendarLeft =
        scrollContainerRef.current?.getBoundingClientRect().left ?? 0;
      const correctedLeft = translateToLeft - calendarLeft + scrollLeft;
      const newStart = calcTimeFromLeft(correctedLeft, selectedDate);

      if (task) {
        const remainedMs = getRemainingTime(task.baseTask);
        const newEnd = (() => {
          if (remainedMs > 0) {
            return new Date(newStart.getTime() + remainedMs);
          } else {
            // 残り時間がマイナスの時は一旦30分で入れる
            return new Date(newStart.getTime() + 30 * 60 * 1000);
          }
        })();
        const newTask: BaseTask = {
          ...task.baseTask,
          assignedName: String(over?.id) ?? "",
          start: newStart,
          end: newEnd,
        };

        updateTask(newTask);
      }
    } else if (over?.id == "carryover") {
      // →繰り越し
      setTimeout(() => {
        setActiveItem(null);
        setActiveBacklogItem(null);
        setIsDragging(false);
      }, 0);

      const cardId = active.id;
      const task = currentDateTasks.find((task) => task.cardId == cardId);
      if (task && task.status !== "未着手") {
        const taskToUpdate = {
          ...task.baseTask,
          status: "中断",
          start: undefined,
          end: undefined,
        }; // 予定日時を空にする
        const formattedTask = dataAlignerOnEdit(taskToUpdate, tasks);
        updateTask(formattedTask);
      }
    } else if (
      activeBacklogItem?.from == "backlog" &&
      over?.id != "backlog" &&
      over?.id != "carryover"
    ) {
      // バックログ→カレンダー
      setTimeout(() => {
        setActiveBacklogItem(null);
        setIsDragging(false);
      }, 0);

      const cardId = active.id;
      const task = backlogTasks.find(
        (task) => task.cardId == cardId
      ) as CardTask;

      const movedTo = event.active.rect.current.translated;
      const translateToLeft = (dragStartLeft ?? 0) + (movedTo?.left ?? 0);
      const scrollLeft = scrollContainerRef.current?.scrollLeft ?? 0;
      const calendarLeft =
        scrollContainerRef.current?.getBoundingClientRect().left ?? 0;
      const correctedLeft = translateToLeft - calendarLeft + scrollLeft;
      const newStart = calcTimeFromLeft(correctedLeft, selectedDate);
      const durationMs = (() => {
        if (task.scheduledTime) {
          return task.scheduledTime * 60 * 1000;
        } else {
          return 60 * 60 * 1000;
        }
      })();
      const newEnd = new Date(newStart.getTime() + durationMs);

      const newTask: BaseTask = {
        ...task.baseTask,
        assignedName: over?.id ? String(over?.id) : undefined,
        start: newStart,
        end: newEnd,
      };
      updateTask(newTask);
    } else if (over?.id == "backlog") {
      // →バックログ
      setTimeout(() => {
        setActiveItem(null);
        setActiveBacklogItem(null);
        setIsDragging(false);
      }, 0);

      const cardId = active.id;
      const task = currentDateTasks.find(
        (task) => task.cardId == cardId
      ) as CardTask;
      if (task) {
        const newTask = {
          ...task.baseTask,
          start: undefined,
          end: undefined,
          status: "未着手",
        };
        updateTask(newTask);
      }
    } else {
      // カレンダー→カレンダー
      setTimeout(() => {
        setActiveItem(null);
        setIsDragging(false);
      }, 0);
      const movedTo = active.rect.current.translated;
      const newTimeCalc = (task: CardTask) => {
        if (movedTo && task.start instanceof Date && task.end instanceof Date) {
          const scrollLeft = scrollContainerRef.current?.scrollLeft ?? 0;
          const movedHour =
            Math.round(
              ((movedTo.left + scrollLeft - (dragStartLeft ?? 0)) / hourWidth) *
                4
            ) /
              4 -
            1.5;
          const takeTime = task.end.getTime() - task.start.getTime();
          const newStartTime = new Date(
            task.start.getTime() + movedHour * 60 * 60 * 1000
          );
          const newEndTime = new Date(newStartTime.getTime() + takeTime);
          return { start: newStartTime, end: newEndTime };
        }
        return { start: task.start, end: task.end };
      };

      const cardId = active.id;
      const task = currentDateTasks.find(
        (task) => task.cardId == cardId
      ) as CardTask;
      updateTask({
        ...task.baseTask,
        start: newTimeCalc(task).start,
        end: newTimeCalc(task).end,
        assignedName: over?.id.toString() ?? task.assignedName,
      });
    }
  };

  const onDragStart = ({ active }: DragStartEvent) => {
    const type = active.data.current?.type;
    if (type == "task") {
      const task = active.data.current?.item as CardTask;
      const pos = cardPositions[task.cardId];
      if (pos) {
        setDragStartLeft(pos.sxLeft);
        setActiveItem(active.data.current as DragData);
      } else {
        const rect = active.rect.current.initial?.left ?? 0;
        setDragStartLeft(rect);
        setActiveBacklogItem(active.data.current as DragData);
      }
      setIsDragging(true);
    }
  };

  return {
    activeItem,
    setActiveItem,
    activeBacklogItem,
    setActiveBacklogItem,
    isDragging,
    setIsDragging,
    dragStartLeft,
    setDragStartLeft,
    handleDragEnd,
    onDragStart,
  };
}
