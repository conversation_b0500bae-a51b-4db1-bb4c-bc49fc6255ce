import { useState } from "react";
import Router from "./routes";
import "./index.css";
import UserProvider from "./utils/AccountContext";
import { DialogProvider } from "./utils/DialogContext";
import { TaskProvider } from "./utils/TaskContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createTheme, ThemeProvider, useTheme } from "@mui/material";
import { jaJP } from "@mui/material/locale";

const queryClient = new QueryClient();
const App = () => {
  const [username, setUsername] = useState("");
  const theme = useTheme();
  return (
    <ThemeProvider theme={createTheme(theme, jaJP)}>
      <UserProvider value={{ username, setUsername }}>
        <QueryClientProvider client={queryClient}>
          <DialogProvider>
            <TaskProvider>
              <Router />
            </TaskProvider>
          </DialogProvider>
        </QueryClientProvider>
      </UserProvider>
    </ThemeProvider>
  );
};

export default App;
