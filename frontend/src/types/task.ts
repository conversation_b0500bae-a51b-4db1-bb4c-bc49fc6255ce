export type TaskHistory = {
  name: string;
  start: Date;
  end: Date;
  status: string;
};

export type DialogMode = "view" | "edit" | "historyEdit" | "new" | undefined;

export type BaseTask = {
  taskId: string;
  taskName: string;
  status: string;
  category?: string;
  location?: string;
  scheduledTime?: number;
  details?: string;
  source?: string;
  assignedName?: string;
  start?: Date | null;
  end?: Date | null;
  dueDate?: Date | null;
  history?: TaskHistory[];
};

export type CardTask = {
  cardId: string;
  isHistoryData: boolean;
  baseTask: BaseTask; // この項目いらないかも
  // ここから下はBaseTaskと共通
  taskId: string;
  taskName: string;
  status: string;
  category?: string;
  location?: string;
  scheduledTime?: number;
  details?: string;
  source?: string;
  assignedName?: string;
  start?: Date | null;
  end?: Date | null;
  dueDate?: Date | null;
};

// // ダイアログ用拡張型
export type DialogTask = Omit<BaseTask, "history"> & {
  mode: DialogMode;
  history: Partial<TaskHistory>[];
};

/**
 * ここから下は型ガード関数
 */
export function isTaskHistory(obj: unknown): obj is TaskHistory {
  if (!obj || typeof obj !== "object") {
    return false;
  }
  const candidate = obj as Record<string, unknown>;

  return (
    typeof candidate.name === "string" &&
    candidate.start instanceof Date &&
    candidate.end instanceof Date &&
    typeof candidate.status === "string"
  );
}

export function isBaseTask(obj: unknown): obj is BaseTask {
  if (!obj || typeof obj !== "object") {
    return false;
  }

  const o = obj as Record<string, unknown>;

  // history配列のチェックだけは事前に行う
  if (o.history !== undefined) {
    if (
      !Array.isArray(o.history) ||
      !o.history.every((item) => isTaskHistory(item))
    ) {
      return false;
    }
  }

  return (
    // 必須プロパティ
    typeof o.taskId === "string" &&
    typeof o.taskName === "string" &&
    typeof o.status === "string" &&
    // オプショナル文字列プロパティ
    (o.category === undefined || typeof o.category === "string") &&
    (o.location === undefined || typeof o.location === "string") &&
    (o.details === undefined || typeof o.details === "string") &&
    (o.source === undefined || typeof o.source === "string") &&
    (o.assignedName === undefined || typeof o.assignedName === "string") &&
    // scheduledTime
    (o.scheduledTime === undefined || typeof o.scheduledTime === "number") &&
    // 日付関連プロパティ
    (o.start === undefined || o.start === null || o.start instanceof Date) &&
    (o.end === undefined || o.end === null || o.end instanceof Date) &&
    (o.dueDate === undefined || o.dueDate === null || o.dueDate instanceof Date)
  );
}
