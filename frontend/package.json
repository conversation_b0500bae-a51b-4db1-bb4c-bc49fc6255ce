{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.682.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/joy": "^5.0.0-beta.51", "@mui/material": "^6.2.0", "@mui/x-data-grid": "^7.22.2", "@mui/x-data-grid-generator": "^7.22.2", "@mui/x-date-pickers": "^7.23.3", "@tanstack/react-query": "^5.62.8", "axios": "^1.7.7", "browser-image-compression": "^2.0.2", "jspdf": "^3.0.0", "jspdf-autotable": "^5.0.2", "material-react-table": "^3.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.2.2", "vite": "^6.2.0"}}