services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=local
      - REGION_NAME=${REGION_NAME}
      - MFA_SERIAL_NUMBER=${MFA_SERIAL_NUMBER}
      - TABLE_NAME=${TABLE_NAME}
      - S3_BUCKET=${S3_BUCKET}
      - COGNITO_USER_POOL_ID=${COGNITO_USER_POOL_ID}
      - COGNITO_CLIENT_ID=${COGNITO_CLIENT_ID}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS}
      - COOKIE_SECURE=${COOKIE_SECURE}
      - SECRET_KEY=${SECRET_KEY}
      - AWS_CLI_PROFILE=${AWS_CLI_PROFILE:-default}
    volumes:
      # <File location in local>:<File location in docker>
      - .:/code
      - ~/.aws:/root/.aws
    stdin_open: true # Allows the container to receive input. docker run -it <container_name>の**-i**にあたる設定です
    tty: true # Creates a terminal interface, making it possible to interact with the container. docker run -it <container_name>の**-t**にあたる設定です。
