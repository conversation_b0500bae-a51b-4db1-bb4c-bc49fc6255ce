FROM python:3.13-alpine

WORKDIR /code

# Install system dependencies including build tools
RUN apk update && apk add --no-cache \
    dos2unix \
    gcc \
    libmagic \
    linux-headers \
    musl-dev \
    python3-dev

# Create directory for AWS credentials cache
RUN mkdir -p /root/.aws && chmod 700 /root/.aws

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# Remove build dependencies to reduce image size
RUN apk del gcc python3-dev musl-dev linux-headers

COPY ./app /code/app
COPY ./init_mfa.py /code/

# Copy and prepare entrypoint script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN dos2unix /docker-entrypoint.sh && \  
    chmod +x /docker-entrypoint.sh

# Use entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]

CMD ["uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]