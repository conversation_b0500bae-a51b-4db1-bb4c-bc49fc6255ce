<!-- websocket 挙動確認用コード。チャット機能実装後に消します -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FastAPI Websocket Demo</title>
</head>
<body>
  <h1>FastAPI Websocket Demo</h1>
  <form>
    <label for="message">Message</label>
    <p>
      <input type="text" name="message" id="message">
      <button type="submit">Send</button>
    </p>
  </form>
  <script>
    const ws = new WebSocket("ws://localhost:8000/ws")

    ws.onopen = () => {
      console.log('Connected to WebSocket Server')
    }

    ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      console.log(message)
    }

    ws.onerror = (error) => {
      console.error('WebSocket Error:', error)
    }

    ws.onclose = () => {
      console.log('WebSocket connection closed')
      console.warn(`:warning: WebSocket closed (Code: ${event.code}, Reason: ${event.reason})`);
    }

    const form = document.querySelector('form')

    form.addEventListener('submit', () => {
      const formData = new FormData(form);
      const message = formData.get('message');

      ws.send(JSON.stringify({type: 'text', content: message}))
      event.preventDefault()
    })
  </script>
</body>
</html>