import os
import sys
import time
from app.aws_config import aws_manager

def main():
    if os.getenv("APP_ENV") != "local":
        print("This script is only for local development")
        sys.exit(1)
        
    try:
        # Check if we have an environment variable for the MFA token
        mfa_token = os.getenv("MFA_TOKEN")
        
        if not mfa_token:
            # If no environment variable, try to read from stdin with a timeout
            print("Enter your MFA token:", flush=True)
            # Give user 30 seconds to input the token
            for _ in range(30):
                if sys.stdin.isatty():  # Only try to read if we have a terminal
                    try:
                        mfa_token = input().strip()
                        break
                    except EOFError:
                        pass
                time.sleep(1)
            
            if not mfa_token:
                print("No MFA token provided. Please run with docker-compose run api")
                sys.exit(1)
        
        # Initialize session with MFA
        aws_manager.initialize_local_session(mfa_token)
        
        print("Successfully initialized MFA session!")
        print("You can now run your application normally")
        
    except Exception as e:
        print(f"Error initializing MFA session: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()