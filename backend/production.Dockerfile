FROM python:3.13-alpine

WORKDIR /code

# Install build dependencies and runtime dependencies
RUN apk update && apk add --no-cache \
    gcc \
    libmagic \
    linux-headers \
    musl-dev \
    python3-dev

# Copy and install requirements
COPY requirements.txt /code/requirements.txt
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# Remove build dependencies to reduce image size
RUN apk del gcc python3-dev musl-dev linux-headers

# Copy application code
COPY ./app /code/app

# Set production environment
ENV APP_ENV=production

# Production-ready command
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]