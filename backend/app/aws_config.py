from typing import Optional, Dict
import boto3
from pydantic import BaseModel
import os
from functools import lru_cache
from .config import settings
from .mfa_manager import MFAManager

class ServiceClients:
    """Container for AWS service clients"""
    def __init__(self, session: boto3.Session):
        self._session = session
        self._clients: Dict[str, any] = {}
        self._resources: Dict[str, any] = {}
    
    def get_client(self, service_name: str):
        """Get or create a service client"""
        if service_name not in self._clients:
            self._clients[service_name] = self._session.client(service_name)
        return self._clients[service_name]
    
    def get_resource(self, service_name: str):
        """Get or create a service resource"""
        if service_name not in self._resources:
            self._resources[service_name] = self._session.resource(service_name)
        return self._resources[service_name]

class AWSResourceManager:
    def __init__(self):
        # Get settings using the function
        self.env = os.getenv("APP_ENV", "production")
        self.profile_name = os.getenv("AWS_CLI_PROFILE", "default")
        print(self.profile_name)
        self._session = None
        self._clients = None
        
        # Initialize MFA manager for local development
        if self.env == "local":
            self.mfa_manager = MFAManager(
                region_name=settings.REGION_NAME,
                serial_number=settings.MFA_SERIAL_NUMBER,
                profile_name=self.profile_name
            )
    
    def initialize_local_session(self, mfa_token: Optional[str] = None):
        """Initialize session with MFA for local development"""
        if self.env != "local":
            raise RuntimeError("MFA initialization only needed in local environment")
        
        self._session = self.mfa_manager.create_boto3_session(mfa_token)
        self._clients = ServiceClients(self._session)
    
    @property
    def session(self) -> boto3.Session:
        """Get the current boto3 session"""
        if not self._session:
            if self.env == "production":
                # In ECS, use instance role credentials
                self._session = boto3.Session(region_name=settings.REGION_NAME)
            elif self.env == "local":
                # For local development, use cached MFA credentials
                self._session = self.mfa_manager.create_boto3_session()
            else:
                raise ValueError(f"Unsupported environment: {self.env}")
        
        return self._session
    
    @property
    def clients(self) -> ServiceClients:
        """Get service clients container"""
        if not self._clients:
            self._clients = ServiceClients(self.session)
        return self._clients
    
    # Convenience properties for commonly used services
    @property
    def dynamodb(self):
        return self.clients.get_resource('dynamodb')
    
    @property
    def s3_client(self):
        return self.clients.get_client('s3')
    
    @property
    def s3_resource(self):
        return self.clients.get_resource('s3')

# Create a singleton instance
aws_manager = AWSResourceManager()