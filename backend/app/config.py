from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List, Dict, Optional
from functools import lru_cache

class Settings(BaseSettings):
    # Environment and AWS Basic Settings
    APP_ENV: str = "production"
    REGION_NAME: str
    TABLE_NAME: str

    # AWS Service Configuration
    S3_BUCKET: str
    MFA_SERIAL_NUMBER: Optional[str] = None
    
    AWS_CLI_PROFILE: str
    
    # Cognito Authentication Settings
    COGNITO_USER_POOL_ID: str
    COGNITO_CLIENT_ID: str
    
    # Security and Session Management
    COOKIE_SECURE: bool = True
    COOKIE_DOMAIN: Optional[str] = None
    SESSION_COOKIE_NAME: str = "session_id"
    SECRET_KEY: str
    
    # Session Timing Configuration
    SESSION_DURATION: int = 8 * 60 * 60  # 8 hours
    SESSION_IDLE_TIMEOUT: int = 8 * 60 * 60
    REFRESH_BUFFER: int = 10
    
    # CORS Configuration
    ALLOWED_ORIGINS: str = Field(
        default="https://d3txh8vvmk4tju.cloudfront.net,http://localhost:3000,http://localhost:5173"
    )
    
    # Resource Limits
    MAX_UPLOAD_SIZE: int = 1024 * 1024
    
    # Feature Flags
    ENABLE_MFA_CACHING: bool = True

    @property
    def origins_list(self) -> List[str]:
        """Convert the ALLOWED_ORIGINS string into a list of origins."""
        if not self.ALLOWED_ORIGINS:
            return ["http://localhost:3000", "http://localhost:5173", "https://d3txh8vvmk4tju.cloudfront.net"]
        return [origin.strip() for origin in self.ALLOWED_ORIGINS.split(",")]

    @property
    def is_development(self) -> bool:
        return self.APP_ENV.lower() in ('development', 'local')
    
    @property
    def requires_mfa(self) -> bool:
        return self.is_development and self.ENABLE_MFA_CACHING

    class Config:
        env_file = ".env"
        case_sensitive = True

# Create a single instance that can be imported directly
settings = Settings()

# Keep the get_settings function for flexibility
@lru_cache()
def get_settings() -> Settings:
    return settings

# from pydantic_settings import BaseSettings

# class Settings(BaseSettings):
#     REGION_NAME: str
#     TABLE_NAME: str
#     MFA_SERIAL_NUMBER: str
#     COGNITO_USER_POOL_ID: str
#     COGNITO_CLIENT_ID: str
#     COOKIE_SECURE: bool = True
#     REFRESH_BUFFER: int = 10
#     SESSION_COOKIE_NAME: str = "session_id"
#     SESSION_DURATION: int = 24 * 60 * 60  # 24 hours
#     SESSION_IDLE_TIMEOUT: int = 30 * 60  # 30 minutes
#     COOKIE_DOMAIN: str | None = None
    
#     class Config:
#         env_file = ".env"
#         case_sensitive = True

# settings = Settings()