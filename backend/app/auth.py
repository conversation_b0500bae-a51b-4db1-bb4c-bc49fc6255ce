import boto3
from fastapi import HTTPException, status, Request
from typing import Dict, Optional
from botocore.exceptions import Client<PERSON>rror
from .config import settings

class CognitoAuthManager:
    def __init__(self, session_store=None):
        self.session_store = session_store
        self.client = boto3.client('cognito-idp', region_name=settings.REGION_NAME)
        self.user_pool_id = settings.COGNITO_USER_POOL_ID
        self.client_id = settings.COGNITO_CLIENT_ID

    async def authenticate_user(self, username: str, password: str) -> Dict:
        """Authenticate user with Cognito"""
        try:
            response = self.client.initiate_auth(
                ClientId=self.client_id,
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': username,
                    'PASSWORD': password
                }
            )
            
            # Get user attributes from Cognito
            user_response = self.client.get_user(
                AccessToken=response['AuthenticationResult']['AccessToken']
            )
            
            # Extract user attributes
            user_attrs = {attr['Name']: attr['Value'] for attr in user_response['UserAttributes']}
            
            # Create session data
            user_data = {
                'username': username,
                'user_attributes': user_attrs,
                'cognito_username': user_response['Username']
            }
            
            return user_data
            
        except self.client.exceptions.NotAuthorizedException:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password"
            )
        except ClientError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

    async def validate_session_security(self, session_id: str, request: Request) -> bool:
        """
        Validates the security of a session by comparing the current request's
        metadata with the metadata stored when the session was created.
        
        This helps detect potential session hijacking attempts where someone
        might try to use a stolen session cookie from a different browser
        or location.
        
        Args:
            session_id: The session identifier to validate
            request: The current FastAPI request object
            
        Returns:
            bool: True if the session is secure, False otherwise
        """
        if not self.session_store:
            raise RuntimeError("Session store not initialized")
            
        # Get the stored session
        session = await self.session_store.get_session(session_id)
        if not session:
            return False
            
        # Get the stored metadata from when the session was created
        metadata = session.get("user_data", {}).get("session_metadata", {})
        if not metadata:
            return False
            
        # Compare the current request's User-Agent with the stored one
        current_user_agent = request.headers.get("user-agent")
        stored_user_agent = metadata.get("user_agent")
        
        if stored_user_agent and current_user_agent != stored_user_agent:
            print(f"User-Agent mismatch detected for session {session_id}")
            return False
            
        # Compare the current request's IP with the stored one
        current_ip = request.headers.get("x-forwarded-for")
        stored_ip = metadata.get("client_ip")
        
        if stored_ip and current_ip != stored_ip:
            print(f"IP address mismatch detected for session {session_id}")
            return False
            
        return True

    async def get_current_session(self, session_id: str, request: Request = None) -> Optional[Dict]:
        """
        Enhanced version of get_current_session that includes security validation.
        
        Args:
            session_id: The session identifier
            request: The current FastAPI request object (optional)
            
        Returns:
            Optional[Dict]: The session data if valid, None otherwise
        """
        if not self.session_store:
            raise RuntimeError("Session store not initialized")
            
        session = await self.session_store.get_session(session_id)
        if not session:
            return None

        # If a request object is provided, perform security validation
        if request and not await self.validate_session_security(session_id, request):
            print(f"Security validation failed for session {session_id}")
            await self.session_store.delete_session(session_id)
            return None

        return session

    def set_session_store(self, store):
        """Set the session store implementation"""
        self.session_store = store