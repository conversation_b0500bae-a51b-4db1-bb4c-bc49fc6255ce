import boto3
import json
import time
import uuid
from abc import ABC, abstractmethod
from typing import Optional, Dict
from .config import settings

# Abstract base class for session storage
class SessionStore(ABC):
    @abstractmethod
    async def create_session(self, user_data: Dict) -> str:
        pass

    @abstractmethod
    async def get_session(self, session_id: str) -> Optional[Dict]:
        pass

    @abstractmethod
    async def update_session(self, session_id: str, data: Dict) -> bool:
        pass

    @abstractmethod
    async def delete_session(self, session_id: str) -> None:
        pass

# In-memory implementation for development
class InMemorySessionStore(SessionStore):
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
        self._cleanup_interval = 3600  # Cleanup every hour
        self._last_cleanup = time.time()

    async def create_session(self, user_id: str, user_data: Dict) -> str:
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "user_id": user_id,
            "user_data": user_data,
            "created_at": time.time(),
            "last_accessed": time.time(),
            "expires_at": time.time() + (24 * 60 * 60)  # 24 hours from now
        }
        return session_id

    async def get_session(self, session_id: str) -> Optional[Dict]:
        session = self.sessions.get(session_id)
        if not session:
            return None
        
        if time.time() > session["expires_at"]:
            await self.delete_session(session_id)
            return None
            
        # Update last accessed time
        session["last_accessed"] = time.time()
        return session
    async def update_session(self, session_id: str, data: Dict) -> bool:
        if session_id not in self.sessions:
            return False
        self.sessions[session_id].update(data)
        self.sessions[session_id]["last_accessed"] = time.time()
        return True

    async def delete_session(self, session_id: str) -> None:
        self.sessions.pop(session_id, None)

    async def _cleanup_expired_sessions(self):
        """Periodically clean up expired sessions to prevent memory leaks"""
        current_time = time.time()
        if current_time - self._last_cleanup > self._cleanup_interval:
            expired_sessions = [
                sid for sid, session in self.sessions.items() 
                if current_time > session["expires_at"]
            ]
            for sid in expired_sessions:
                await self.delete_session(sid)
            self._last_cleanup = current_time