from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response, Cookie, status, Header
from fastapi.middleware.cors import CORSMiddleware
from boto3.dynamodb.conditions import Key, Attr

from datetime import datetime, timedelta
from dateutil import parser

from pydantic import BaseModel, Field
from typing import Optional
import time

from .config import settings
from .session_store import InMemorySessionStore
from .auth import CognitoAuthManager
from .aws_config import aws_manager

import boto3
import os

app_env = os.getenv("APP_ENV")
region_name = os.getenv("REGION_NAME")
table_name = os.getenv("TABLE_NAME")

client = boto3.client("sts")

app = FastAPI()

# CORS middleware setup
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.origins_list,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Initialize services
auth_manager = CognitoAuthManager()
session_store = InMemorySessionStore()
auth_manager.set_session_store(session_store)

# Get DynamoDB table reference
table = ""
# コンテナ起動時にこの分岐に入る
if app_env == "local":
    table = aws_manager.dynamodb.Table(settings.TABLE_NAME)


# ローカル環境用
if app_env == "isecloud":
    serial_number = os.getenv("MFA_SERIAL_NUMBER")

    mfa_otp = input("Enter the MFA code: ")
    mfa_creds = client.get_session_token(
        DurationSeconds=36000, SerialNumber=serial_number, TokenCode=mfa_otp
    )

    dynamodb = boto3.resource(
        service_name="dynamodb",
        region_name=region_name,
        aws_access_key_id=mfa_creds["Credentials"]["AccessKeyId"],
        aws_secret_access_key=mfa_creds["Credentials"]["SecretAccessKey"],
        aws_session_token=mfa_creds["Credentials"]["SessionToken"],
    )
    table = dynamodb.Table(table_name)

    s3 = boto3.client(
        "s3",
        region_name=region_name,
        aws_access_key_id=mfa_creds["Credentials"]["AccessKeyId"],
        aws_secret_access_key=mfa_creds["Credentials"]["SecretAccessKey"],
        aws_session_token=mfa_creds["Credentials"]["SessionToken"],
    )
    s3_download = boto3.resource(
        "s3",
        region_name=region_name,
        aws_access_key_id=mfa_creds["Credentials"]["AccessKeyId"],
        aws_secret_access_key=mfa_creds["Credentials"]["SecretAccessKey"],
        aws_session_token=mfa_creds["Credentials"]["SessionToken"],
    )


class LoginRequest(BaseModel):
    username: str
    password: str


class LogoutResponse(BaseModel):
    message: str = "Successfully logged out"


from datetime import datetime, timezone
from typing import List


def pascal_to_camel(name: str) -> str:
    return name[:1].lower() + name[1:] if name else name


def camel_to_pascal(name: str) -> str:
    return name[:1].upper() + name[1:] if name else name


class TaskHistory(BaseModel):
    Name: str
    Start: datetime
    End: datetime
    Status: str


class TaskCreate(BaseModel):
    TaskId: str
    TaskName: str
    Status: str
    Category: str
    Location: str
    ScheduledTime: Optional[int] = None
    Details: Optional[str] = None
    Source: str
    AssignedName: str
    Start: Optional[datetime] = None
    End: Optional[datetime] = None
    DueDate: Optional[datetime] = None
    WorkedTime: Optional[int] = None
    History: Optional[List[TaskHistory]] = None


# Cookie settings
COOKIE_NAME = "session_id"


@app.post("/auth/login")
async def login(
    login_data: LoginRequest,
    response: Response,
    user_agent: str = Header(None),
    client_ip: str = Header(None, alias="X-Forwarded-For"),
):
    try:
        # Authenticate with Cognito
        user_data = await auth_manager.authenticate_user(
            username=login_data.username, password=login_data.password
        )

        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication failed"
            )

        # Add session metadata
        session_metadata = {
            "user_agent": user_agent,
            "client_ip": client_ip,
            "login_time": time.time(),
        }

        # Create session
        session_id = await session_store.create_session(
            user_id=user_data["cognito_username"],
            user_data={**user_data, "session_metadata": session_metadata},
        )

        # Set session cookie
        response.set_cookie(
            key=settings.SESSION_COOKIE_NAME,
            value=session_id,
            max_age=settings.SESSION_DURATION,
            httponly=True,
            secure=settings.COOKIE_SECURE,
            samesite="None",  # Changed from "lax" for better security
            domain=settings.COOKIE_DOMAIN,
        )

        return {
            "message": "Successfully logged in",
            "username": user_data["username"],
            "user_data": user_data,
            "session_id": session_id,
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))


@app.post("/auth/logout")
async def logout(
    response: Response, session_id: Optional[str] = Cookie(None, alias=COOKIE_NAME)
):
    if not session_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="No active session found"
        )

    # First verify if the session exists
    session = await session_store.get_session(session_id)
    if not session:
        response.delete_cookie(  # Still delete the cookie even if session doesn't exist
            key=COOKIE_NAME,
            httponly=True,
            secure=settings.COOKIE_SECURE,
            samesite="lax",
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session",
        )

    # If we get here, we have a valid session to delete
    try:
        await session_store.delete_session(session_id)
        response.delete_cookie(
            key=COOKIE_NAME,
            httponly=True,
            secure=settings.COOKIE_SECURE,
            samesite="lax",
        )
        return {"message": "Successfully logged out", "status": "success"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error occurred during logout",
        )


from fastapi.encoders import jsonable_encoder


@app.post("/tasks")
async def create_task(request: dict):
    try:
        # camelCaseをPascalCaseに変換する
        pascal_case_data = {}
        for key, value in request.items():
            # ネストされたhistory配列を処理
            if key == "history" and isinstance(value, list):
                pascal_case_data["History"] = []
                for history_item in value:
                    pascal_history_item = {}
                    for h_key, h_value in history_item.items():
                        pascal_history_item[camel_to_pascal(h_key)] = h_value
                    pascal_case_data["History"].append(pascal_history_item)
            else:
                pascal_case_data[camel_to_pascal(key)] = value

        # 変換後のデータでTaskCreateオブジェクトを作成
        task = TaskCreate(**pascal_case_data)
        item = jsonable_encoder(task)
        # DynamoDB put_item
        full_item = {**item, "Area#Store#MD": "Test"}
        table.put_item(Item=full_item)
        return {"message": "Task created successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating task: {str(e)}")


@app.put("/task")
async def update_task(request: dict):
    try:
        # camelCaseをPascalCaseに変換する
        pascal_case_data = {}
        for key, value in request.items():
            # ネストされたhistory配列を処理
            if key == "history" and isinstance(value, list):
                pascal_case_data["History"] = []
                for history_item in value:
                    pascal_history_item = {}
                    for h_key, h_value in history_item.items():
                        pascal_history_item[camel_to_pascal(h_key)] = h_value
                    pascal_case_data["History"].append(pascal_history_item)
            else:
                pascal_case_data[camel_to_pascal(key)] = value

        # 変換後のデータでTaskCreateオブジェクトを作成
        task = TaskCreate(**pascal_case_data)
        item = jsonable_encoder(task)
        # DynamoDB put_item
        full_item = {**item, "Area#Store#MD": "Test"}
        table.put_item(Item=full_item)
        return {"message": f"Task {task.TaskId} updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating task: {str(e)}")


@app.get("/tasks")
def get_tasks(area_id: str, date: str, assigned_name: Optional[str] = None):
    """
    - area_id: エリアID 例："Area#Store#MD"の値
    - date: 対象日（形式：YYYY-MM-DD）、前日・当日・翌日のタスクをフィルタリングするために使用
    - assigned_name【option】: 担当者名（AssignedNameフィールドと一致する値のみ返却、未指定時は全件返却）
    """
    try:
        if assigned_name:
            tasks = table.query(
                KeyConditionExpression=Key("Area#Store#MD").eq(area_id),
                FilterExpression=Attr("AssignedName").eq(assigned_name),
            )
        else:
            tasks = table.query(
                KeyConditionExpression=Key("Area#Store#MD").eq(area_id),
            )
        result = tasks.get("Items", [])

        def is_in_date_range(start_str, end_str):
            if not all(
                isinstance(s, str) and s.endswith("Z") for s in [start_str, end_str]
            ):
                return False
            if not isinstance(date, str):
                return False
            JST = timezone(timedelta(hours=9))
            try:
                start = parser.isoparse(start_str).astimezone(JST)
                end = parser.isoparse(end_str).astimezone(JST)
                base_date = datetime.strptime(date, "%Y-%m-%d").replace(tzinfo=JST)
            except ValueError:
                return False

            # 前日21:00 〜 翌日9:00
            range_start = (base_date - timedelta(days=1)).replace(
                hour=21, minute=0, second=0
            )
            range_end = (base_date + timedelta(days=1)).replace(
                hour=9, minute=0, second=0
            )

            # 重なり判定
            return not (end <= range_start or start >= range_end)

        def has_null_start_end(item):
            return (item.get("Start") in [None, ""]) or (item.get("End") in [None, ""])

        def history_has_in_date_range(item):
            history = item.get("History", [])
            if history:
                for h in history:
                    if is_in_date_range(h.get("Start"), h.get("End")):
                        return True
            return False

        def lowercase_keys(data):
            if isinstance(data, dict):
                return {
                    (
                        k[0].lower() + k[1:] if isinstance(k, str) and k else k
                    ): lowercase_keys(v)
                    for k, v in data.items()
                }
            elif isinstance(data, list):
                return [lowercase_keys(item) for item in data]
            else:
                return data

        filtered_tasks = [
            item
            for item in result
            if (
                is_in_date_range(item.get("Start"), item.get("End"))
                or history_has_in_date_range(item)
                or has_null_start_end(item)
            )
        ]
        converted_tasks = lowercase_keys(filtered_tasks)

        return {"tasks": converted_tasks}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tasks: {str(e)}")


@app.delete("/task")
async def delete_item(task_id: str):
    try:
        table.delete_item(
            Key={
                "Area#Store#MD": "Test",
                "TaskId": task_id,
            }
        )
        return {"message": f"Task {task_id} deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tasks: {str(e)}")


@app.get("/weeklytasks")
def get_weekly_tasks(area_id: str, start_date: str, end_date: str):
    """
    - area_id: エリアID 例："Area#Store#MD"の値
    - start_date: 取得する範囲の開始日（形式：YYYY-MM-DD）
    - start_date: 取得する範囲の終了日（形式：YYYY-MM-DD）
    """
    try:
        tasks = table.query(
            KeyConditionExpression=Key("Area#Store#MD").eq(area_id),
        )
        result = tasks.get("Items", [])

        def is_in_date_range(start_str, end_str):
            if not all(
                isinstance(s, str) and s.endswith("Z") for s in [start_str, end_str]
            ):
                return False
            if not isinstance(start_date, str):
                return False
            if not isinstance(end_date, str):
                return False
            JST = timezone(timedelta(hours=9))
            try:
                start = parser.isoparse(start_str).astimezone(JST)
                end = parser.isoparse(end_str).astimezone(JST)
                base_date_start = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=JST)
                base_date_end = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=JST)
                range_start = base_date_start.replace(
                hour=0, minute=0, second=0
                )
                range_end = base_date_end.replace(hour=0, minute=0, second=0) + timedelta(days=1)
                print(start)
                print(end)
                # print(range_start)
                # print(range_end)
                # print(not (end <= range_start or start >= range_end))
            except ValueError:
                print(ValueError)
                return False

            # 重なり判定
            return not (end <= range_start or start >= range_end)

        def has_null_start_end(item):
            return (item.get("Start") in [None, ""]) or (item.get("End") in [None, ""])

        def history_has_in_date_range(item):
            history = item.get("History", [])
            if history:
                for h in history:
                    if is_in_date_range(h.get("Start"), h.get("End")):
                        return True
            return False

        def lowercase_keys(data):
            if isinstance(data, dict):
                return {
                    (
                        k[0].lower() + k[1:] if isinstance(k, str) and k else k
                    ): lowercase_keys(v)
                    for k, v in data.items()
                }
            elif isinstance(data, list):
                return [lowercase_keys(item) for item in data]
            else:
                return data

        filtered_tasks = [
            item
            for item in result
            if (
                is_in_date_range(item.get("Start"), item.get("End"))
                or history_has_in_date_range(item)
                or has_null_start_end(item)
            )
        ]
        converted_tasks = lowercase_keys(filtered_tasks)

        return {"tasks": converted_tasks}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching tasks: {str(e)}")
