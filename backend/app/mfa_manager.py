import boto3
import json
import os
import time
from pathlib import Path
from typing import Optional, Dict
from pydantic import BaseModel

class MFACredentials(BaseModel):
    AccessKeyId: str
    SecretAccessKey: str
    SessionToken: str
    Expiration: float

class MFAManager:
    def __init__(self, region_name: str, serial_number: str, profile_name: str):
        self.region_name = region_name
        self.serial_number = serial_number
        self.profile_name = profile_name
        self.cache_file = Path.home() / '.aws' / 'mfa_cache.json'
        session = boto3.session.Session(profile_name=profile_name)
        self.sts_client = session.client('sts')
        
    def _load_cached_credentials(self) -> Optional[MFACredentials]:
        """Load cached MFA credentials if they exist and are valid"""
        try:
            if not self.cache_file.exists():
                return None
                
            with open(self.cache_file, 'r') as f:
                data = json.load(f)
                creds = MFACredentials(**data)
                
                # Check if credentials are still valid (with 5 min buffer)
                if creds.Expiration > time.time() + 300:
                    return creds
                    
        except Exception as e:
            print(f"Error loading cached credentials: {e}")
            
        return None
        
    def _save_credentials(self, creds: MFACredentials):
        """Save credentials to cache file"""
        try:
            # Ensure .aws directory exists
            self.cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.cache_file, 'w') as f:
                json.dump(creds.dict(), f)
                
        except Exception as e:
            print(f"Error saving credentials: {e}")
            
    def get_credentials(self, token_code: Optional[str] = None) -> MFACredentials:
        """Get valid MFA credentials, either from cache or by requesting new ones"""
        # Try to load cached credentials first
        creds = self._load_cached_credentials()
        if creds:
            return creds
            
        # If no valid cached credentials and no token provided, raise error
        if not token_code:
            raise ValueError("MFA token required for new session")
            
        # Get new credentials using MFA token
        response = self.sts_client.get_session_token(
            DurationSeconds=36000,  # 10 hours
            SerialNumber=self.serial_number,
            TokenCode=token_code
        )
        
        # Create and cache credentials
        creds = MFACredentials(
            AccessKeyId=response['Credentials']['AccessKeyId'],
            SecretAccessKey=response['Credentials']['SecretAccessKey'],
            SessionToken=response['Credentials']['SessionToken'],
            Expiration=response['Credentials']['Expiration'].timestamp()
        )
        
        self._save_credentials(creds)
        return creds

    def create_boto3_session(self, token_code: Optional[str] = None) -> boto3.Session:
        """Create a boto3 session with valid MFA credentials"""
        creds = self.get_credentials(token_code)
        
        return boto3.Session(
            aws_access_key_id=creds.AccessKeyId,
            aws_secret_access_key=creds.SecretAccessKey,
            aws_session_token=creds.SessionToken,
            region_name=self.region_name,
            profile_name=self.profile_name,
        )