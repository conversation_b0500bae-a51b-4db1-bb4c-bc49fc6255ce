#!/bin/bash
#########################################
# Make it executable
## chmod +x push-to-isecloud.sh
# Run it with your MFA token
## ./push-to-isecloud.sh <mfa-token>
########################################

# Check if MFA token is provided
if [ -z "$1" ]; then
    echo "Please provide your MFA token as an argument"
    echo "Usage: ./push-to-isecloud.sh <mfa-token>"
    exit 1
fi

# Your MFA ARN - replace with your actual MFA ARN
MFA_SERIAL="arn:aws:iam::771205398267:mfa/Qiu_iPhone"

# Get temporary credentials with M<PERSON> and save to a file
echo "Getting temporary credentials with MFA..."
aws sts get-session-token \
    --serial-number $MFA_SERIAL \
    --token-code $1 \
    --duration-seconds 43200 --output text > temp_creds.txt

# Read credentials from the file using cut command
export AWS_ACCESS_KEY_ID=$(grep CREDENTIALS temp_creds.txt | cut -f 2)
export AWS_SECRET_ACCESS_KEY=$(grep CREDENTIALS temp_creds.txt | cut -f 4)
export AWS_SESSION_TOKEN=$(grep CREDENTIALS temp_creds.txt | cut -f 5)

# Clean up the temporary file
rm temp_creds.txt

# Now run the ECR commands
echo "Logging in to ECR..."
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 771205398267.dkr.ecr.us-east-1.amazonaws.com

echo "Building production image..."
docker build --platform linux/amd64 --provenance false -f production.Dockerfile -t dish-cf-ecr-repository .

echo "Tagging image..."
docker tag dish-cf-ecr-repository:latest 771205398267.dkr.ecr.us-east-1.amazonaws.com/dish-cf-ecr-repository:latest

echo "Pushing image to ECR..."
docker push 771205398267.dkr.ecr.us-east-1.amazonaws.com/dish-cf-ecr-repository:latest

echo "Done! Image has been pushed to ECR successfully."