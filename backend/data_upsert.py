# 以下で実行できる
# python3 data_upsert.py [環境変数ファイル] [投入するデータのjsonファイル] [AWS CLIのプロファイル（指定なしの場合default）]
# e.g.) $ python3 data_upsert.py isecloud.env ./taskList.json ppih-isecloud

# 実行後、log_[timestamp].jsonのファイルが出力される

# docker経由の実行ではないので、ローカル/仮想環境にboto3をインストールする必要あり
import json
import boto3
import uuid
import datetime
import os
import sys
from dotenv import load_dotenv


env_file = sys.argv[1]
file_path = sys.argv[2]
try:
    profile_name = sys.argv[3]
except:
    profile_name = "default"
load_dotenv(dotenv_path=env_file)
app_env = os.getenv("APP_ENV")
region_name = os.getenv("REGION_NAME")
table_name = os.getenv("TABLE_NAME")

print("profile is: " + profile_name)

session = boto3.session.Session(profile_name=profile_name)
client = session.client('sts')


# JSONファイルのパス
timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H:%M:%S")
LOG_FILE = 'log_' + timestamp + ".json"



if app_env == "isecloud":
    serial_number = os.getenv("MFA_SERIAL_NUMBER")

    mfa_otp = input("Enter the MFA code: ")
    mfa_creds = client.get_session_token(
        DurationSeconds=36000,
        SerialNumber=serial_number,
        TokenCode=mfa_otp
    )

    dynamodb = session.resource(service_name="dynamodb",
                region_name=region_name,
                aws_access_key_id=mfa_creds['Credentials']['AccessKeyId'],
                aws_secret_access_key=mfa_creds['Credentials']['SecretAccessKey'],
                aws_session_token=mfa_creds['Credentials']['SessionToken'])
    table = dynamodb.Table(table_name)

    cred = mfa_creds['Credentials']

elif app_env == "bih":
    aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
    aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
    aws_session_token = os.getenv("AWS_SESSION_TOKEN")

    dynamodb = session.resource(service_name="dynamodb",
                region_name=region_name,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key,
                aws_session_token=aws_session_token)
    table = dynamodb.Table(table_name)


# JSONファイルを読み込む
with open(file_path, 'r', encoding='utf-8') as f:
    items = json.load(f)

# 全件に対してチェックやID追加
for item in items:
    # パーティションキーの追加
    item['Area#Store#MD'] = 'Test'
    if item['TaskId'] is None:
        item['TaskId'] = uuid.uuid4()
    elif not item['TaskId']:
        item['TaskId'] = uuid.uuid4()

# 各アイテムをDynamoDBに投入
logs = []
for item in items:
    try:
        table.put_item(Item=item)
        logs.append(f"Inserted item: {item}")
    except Exception as e:
        logs.append(f"Error Inserting item: {item}: {e}")
        print(f"Error inserting item {item}: {e}")

with open(LOG_FILE, 'w', encoding='utf-8') as f:
    json.dump(logs, f, ensure_ascii=False, indent=4)

print(f"upsert {len(logs)} data")